# utils/multibroker_master_contract.py

"""
Multi-Broker Master Contract Management Utilities
================================================

This module provides utilities to manage master contracts for multiple brokers
without conflicts. Each broker maintains its own symbol database.
"""

import logging
import importlib
from flask import session
from database.multibroker_master_contract_db import (
    delete_broker_symbols, 
    copy_broker_symbols_from_dataframe,
    get_broker_symbol_count,
    search_broker_symbols,
    get_broker_symbol_info,
    migrate_existing_symbols_to_broker
)
from utils.broker_credentials import inject_broker_credentials, restore_broker_credentials

logger = logging.getLogger(__name__)

# Supported brokers and their master contract modules
BROKER_MASTER_CONTRACT_MODULES = {
    'zerodha': 'broker.zerodha.database.master_contract_db',
    'fyers': 'broker.fyers.database.master_contract_db', 
    'dhan': 'broker.dhan.database.master_contract_db',
    'upstox': 'broker.upstox.database.master_contract_db',
    'angel': 'broker.angel.database.master_contract_db',
    'flattrade': 'broker.flattrade.database.master_contract_db',
    'firstock': 'broker.firstock.database.master_contract_db',
    'paytm': 'broker.paytm.database.master_contract_db',
    'fivepaisa': 'broker.fivepaisa.database.master_contract_db',
    'pocketful': 'broker.pocketful.database.master_contract_db',
    'compositedge': 'broker.compositedge.database.master_contract_db',
    'dhan_sandbox': 'broker.dhan_sandbox.database.master_contract_db'
}

def download_broker_master_contract(broker_name, user_id=None):
    """
    Download master contract for a specific broker
    
    Args:
        broker_name (str): Name of the broker
        user_id (str): User ID for credential injection
        
    Returns:
        dict: Result with status and message
    """
    try:
        logger.info(f"Starting master contract download for {broker_name}")
        
        # Check if broker is supported
        if broker_name not in BROKER_MASTER_CONTRACT_MODULES:
            return {
                'status': 'error',
                'message': f'Broker {broker_name} not supported for master contract download'
            }
        
        # Get user_id from session if not provided
        if not user_id:
            user_id = session.get('user_id') or session.get('user')
        
        # Inject broker credentials for download
        credentials_injected = inject_broker_credentials(broker_name, user_id)
        
        try:
            # Import broker-specific master contract module
            module_path = BROKER_MASTER_CONTRACT_MODULES[broker_name]
            master_contract_module = importlib.import_module(module_path)
            
            # Check if the module has the required functions
            if not hasattr(master_contract_module, 'master_contract_download'):
                return {
                    'status': 'error',
                    'message': f'Master contract download function not found for {broker_name}'
                }
            
            # Get the original download function
            original_download_func = master_contract_module.master_contract_download
            
            # Create a wrapper that uses broker-specific storage
            def broker_specific_download():
                try:
                    # Call the original download function to get the data
                    # We need to intercept the data before it gets stored
                    
                    # For now, let's call the original function and then migrate the data
                    result = original_download_func()
                    
                    # After the original function completes, migrate data to broker-specific table
                    migrate_to_broker_specific_table(broker_name)
                    
                    logger.info(f"Master contract download completed for {broker_name}")
                    return {
                        'status': 'success',
                        'message': f'Successfully downloaded master contract for {broker_name}'
                    }
                    
                except Exception as e:
                    logger.error(f"Error in broker-specific download for {broker_name}: {e}")
                    return {
                        'status': 'error',
                        'message': f'Failed to download master contract for {broker_name}: {str(e)}'
                    }
            
            # Execute the broker-specific download
            result = broker_specific_download()
            
            return result
            
        finally:
            # Always restore credentials
            if credentials_injected:
                restore_broker_credentials()
        
    except Exception as e:
        logger.error(f"Error downloading master contract for {broker_name}: {e}")
        return {
            'status': 'error',
            'message': f'Failed to download master contract for {broker_name}: {str(e)}'
        }

def migrate_to_broker_specific_table(broker_name):
    """
    Migrate data from the old symtoken table to broker-specific table
    This is called after the original download function completes
    """
    try:
        logger.info(f"Migrating symbols to broker-specific table for {broker_name}")
        
        # Delete existing symbols for this broker
        delete_broker_symbols(broker_name)
        
        # Migrate from old table to new broker-specific table
        migrate_existing_symbols_to_broker(broker_name)
        
        # Get count of migrated symbols
        symbol_count = get_broker_symbol_count(broker_name)
        logger.info(f"Successfully migrated {symbol_count} symbols for {broker_name}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error migrating symbols for {broker_name}: {e}")
        return False

def get_broker_master_contract_status(broker_name):
    """
    Get the status of master contract for a broker
    
    Args:
        broker_name (str): Name of the broker
        
    Returns:
        dict: Status information
    """
    try:
        symbol_count = get_broker_symbol_count(broker_name)
        
        return {
            'broker': broker_name,
            'symbol_count': symbol_count,
            'status': 'available' if symbol_count > 0 else 'not_downloaded',
            'supported': broker_name in BROKER_MASTER_CONTRACT_MODULES
        }
        
    except Exception as e:
        logger.error(f"Error getting master contract status for {broker_name}: {e}")
        return {
            'broker': broker_name,
            'symbol_count': 0,
            'status': 'error',
            'supported': False,
            'error': str(e)
        }

def search_symbols_for_broker(broker_name, search_term, limit=50):
    """
    Search symbols for a specific broker
    
    Args:
        broker_name (str): Name of the broker
        search_term (str): Search term
        limit (int): Maximum number of results
        
    Returns:
        list: List of matching symbols
    """
    try:
        return search_broker_symbols(broker_name, search_term, limit)
    except Exception as e:
        logger.error(f"Error searching symbols for {broker_name}: {e}")
        return []

def get_symbol_info_for_broker(broker_name, symbol, exchange):
    """
    Get symbol information for a specific broker
    
    Args:
        broker_name (str): Name of the broker
        symbol (str): Symbol name
        exchange (str): Exchange name
        
    Returns:
        dict: Symbol information or None
    """
    try:
        return get_broker_symbol_info(broker_name, symbol, exchange)
    except Exception as e:
        logger.error(f"Error getting symbol info for {broker_name}: {e}")
        return None

def get_all_brokers_master_contract_status():
    """
    Get master contract status for all supported brokers
    
    Returns:
        dict: Status for all brokers
    """
    try:
        status = {}
        
        for broker_name in BROKER_MASTER_CONTRACT_MODULES.keys():
            status[broker_name] = get_broker_master_contract_status(broker_name)
        
        return status
        
    except Exception as e:
        logger.error(f"Error getting all brokers master contract status: {e}")
        return {}

def search_symbols_for_current_broker(search_term, limit=50):
    """
    Search symbols for the currently selected broker
    
    Args:
        search_term (str): Search term
        limit (int): Maximum number of results
        
    Returns:
        list: List of matching symbols
    """
    try:
        current_broker = session.get('broker')
        
        if not current_broker:
            logger.warning("No broker selected in session")
            return []
        
        return search_symbols_for_broker(current_broker, search_term, limit)
        
    except Exception as e:
        logger.error(f"Error searching symbols for current broker: {e}")
        return []

def download_master_contract_for_configured_brokers(user_id=None):
    """
    Download master contracts for all configured brokers for a user
    
    Args:
        user_id (str): User ID
        
    Returns:
        dict: Results for all brokers
    """
    try:
        # Get user_id from session if not provided
        if not user_id:
            user_id = session.get('user_id') or session.get('user')
        
        if not user_id:
            return {
                'status': 'error',
                'message': 'No user ID available'
            }
        
        # Get configured brokers for user
        from database.broker_config_db import get_all_broker_configs
        broker_configs = get_all_broker_configs(user_id)
        
        results = {}
        
        for config in broker_configs:
            broker_name = config['broker']
            logger.info(f"Downloading master contract for configured broker: {broker_name}")
            
            result = download_broker_master_contract(broker_name, user_id)
            results[broker_name] = result
        
        return {
            'status': 'success',
            'message': f'Processed {len(broker_configs)} configured brokers',
            'results': results
        }
        
    except Exception as e:
        logger.error(f"Error downloading master contracts for configured brokers: {e}")
        return {
            'status': 'error',
            'message': f'Failed to download master contracts: {str(e)}'
        }
