# utils/automated_master_contract.py

"""
Automated Master Contract Management System
==========================================

This module provides automated master contract download functionality:
- Downloads master contracts on user login (once per day)
- Handles all configured brokers automatically
- Manages download scheduling and status tracking
- Provides background download with progress tracking
"""

import logging
import threading
import time
from datetime import datetime, timedelta
from flask import session
from database.broker_config_db import get_user_broker_configs
from utils.multibroker_master_contract import (
    download_broker_master_contract,
    get_broker_master_contract_status,
    get_all_brokers_master_contract_status
)
from database.multibroker_master_contract_db import get_broker_symbol_count
import os
import json

logger = logging.getLogger(__name__)

# Global download status tracking
download_status = {}
download_threads = {}

class MasterContractDownloadManager:
    """Manages automated master contract downloads"""
    
    def __init__(self):
        self.download_status_file = 'tmp/master_contract_status.json'
        self.ensure_tmp_directory()
    
    def ensure_tmp_directory(self):
        """Ensure tmp directory exists"""
        try:
            os.makedirs('tmp', exist_ok=True)
        except Exception as e:
            logger.error(f"Error creating tmp directory: {e}")
    
    def get_last_download_date(self, user_id, broker_name):
        """Get the last download date for a user-broker combination"""
        try:
            if os.path.exists(self.download_status_file):
                with open(self.download_status_file, 'r') as f:
                    status_data = json.load(f)
                
                user_data = status_data.get(str(user_id), {})
                broker_data = user_data.get(broker_name, {})
                last_download = broker_data.get('last_download')
                
                if last_download:
                    return datetime.fromisoformat(last_download)
            
            return None
        except Exception as e:
            logger.error(f"Error reading download status: {e}")
            return None
    
    def set_last_download_date(self, user_id, broker_name, download_date=None):
        """Set the last download date for a user-broker combination"""
        try:
            if download_date is None:
                download_date = datetime.now()
            
            # Load existing data
            status_data = {}
            if os.path.exists(self.download_status_file):
                with open(self.download_status_file, 'r') as f:
                    status_data = json.load(f)
            
            # Update data
            if str(user_id) not in status_data:
                status_data[str(user_id)] = {}
            
            if broker_name not in status_data[str(user_id)]:
                status_data[str(user_id)][broker_name] = {}
            
            status_data[str(user_id)][broker_name]['last_download'] = download_date.isoformat()
            status_data[str(user_id)][broker_name]['symbol_count'] = get_broker_symbol_count(broker_name)
            
            # Save data
            with open(self.download_status_file, 'w') as f:
                json.dump(status_data, f, indent=2)
            
            logger.info(f"Updated download status for user {user_id}, broker {broker_name}")
            
        except Exception as e:
            logger.error(f"Error saving download status: {e}")
    
    def should_download_today(self, user_id, broker_name):
        """Check if master contract should be downloaded today"""
        try:
            last_download = self.get_last_download_date(user_id, broker_name)
            
            if last_download is None:
                logger.info(f"No previous download found for {broker_name} - download needed")
                return True
            
            # Check if last download was today
            today = datetime.now().date()
            last_download_date = last_download.date()
            
            if last_download_date < today:
                logger.info(f"Last download for {broker_name} was {last_download_date} - download needed")
                return True
            
            # Check if symbols exist in database
            symbol_count = get_broker_symbol_count(broker_name)
            if symbol_count == 0:
                logger.info(f"No symbols found for {broker_name} - download needed")
                return True
            
            logger.info(f"Master contract for {broker_name} is up to date ({symbol_count} symbols)")
            return False
            
        except Exception as e:
            logger.error(f"Error checking download status for {broker_name}: {e}")
            return True  # Download on error to be safe
    
    def get_brokers_needing_download(self, user_id):
        """Get list of brokers that need master contract download"""
        try:
            broker_configs = get_user_broker_configs(user_id)
            brokers_needing_download = []
            
            for config in broker_configs:
                broker_name = config['broker_name']
                if self.should_download_today(user_id, broker_name):
                    brokers_needing_download.append(broker_name)
            
            return brokers_needing_download
            
        except Exception as e:
            logger.error(f"Error getting brokers needing download: {e}")
            return []
    
    def download_master_contract_background(self, user_id, broker_name):
        """Download master contract in background thread"""
        try:
            download_key = f"{user_id}_{broker_name}"
            
            # Update status to downloading
            download_status[download_key] = {
                'status': 'downloading',
                'broker': broker_name,
                'start_time': datetime.now().isoformat(),
                'progress': 'Starting download...'
            }
            
            logger.info(f"Starting background download for {broker_name} (user: {user_id})")
            
            # Perform the download
            result = download_broker_master_contract(broker_name, user_id)
            
            if result['status'] == 'success':
                # Update download date
                self.set_last_download_date(user_id, broker_name)
                
                # Update status to completed
                download_status[download_key] = {
                    'status': 'completed',
                    'broker': broker_name,
                    'start_time': download_status[download_key]['start_time'],
                    'end_time': datetime.now().isoformat(),
                    'symbol_count': get_broker_symbol_count(broker_name),
                    'message': result['message']
                }
                
                logger.info(f"Successfully downloaded master contract for {broker_name}")
                
            else:
                # Update status to failed
                download_status[download_key] = {
                    'status': 'failed',
                    'broker': broker_name,
                    'start_time': download_status[download_key]['start_time'],
                    'end_time': datetime.now().isoformat(),
                    'error': result['message']
                }
                
                logger.error(f"Failed to download master contract for {broker_name}: {result['message']}")
            
        except Exception as e:
            download_key = f"{user_id}_{broker_name}"
            download_status[download_key] = {
                'status': 'failed',
                'broker': broker_name,
                'start_time': download_status.get(download_key, {}).get('start_time', datetime.now().isoformat()),
                'end_time': datetime.now().isoformat(),
                'error': str(e)
            }
            logger.error(f"Exception during background download for {broker_name}: {e}")
        
        finally:
            # Clean up thread reference
            download_key = f"{user_id}_{broker_name}"
            if download_key in download_threads:
                del download_threads[download_key]
    
    def start_automated_downloads(self, user_id):
        """Start automated downloads for all brokers needing updates"""
        try:
            brokers_needing_download = self.get_brokers_needing_download(user_id)
            
            if not brokers_needing_download:
                logger.info(f"No brokers need master contract download for user {user_id}")
                return {
                    'status': 'success',
                    'message': 'All master contracts are up to date',
                    'brokers_updated': []
                }
            
            logger.info(f"Starting automated downloads for user {user_id}: {brokers_needing_download}")
            
            # Start background downloads
            for broker_name in brokers_needing_download:
                download_key = f"{user_id}_{broker_name}"
                
                # Check if download is already in progress
                if download_key in download_threads and download_threads[download_key].is_alive():
                    logger.info(f"Download already in progress for {broker_name}")
                    continue
                
                # Start new download thread
                thread = threading.Thread(
                    target=self.download_master_contract_background,
                    args=(user_id, broker_name),
                    daemon=True
                )
                thread.start()
                download_threads[download_key] = thread
                
                # Add small delay between downloads to avoid overwhelming the system
                time.sleep(1)
            
            return {
                'status': 'success',
                'message': f'Started downloads for {len(brokers_needing_download)} brokers',
                'brokers_updating': brokers_needing_download
            }
            
        except Exception as e:
            logger.error(f"Error starting automated downloads: {e}")
            return {
                'status': 'error',
                'message': f'Failed to start automated downloads: {str(e)}'
            }
    
    def get_download_status(self, user_id, broker_name=None):
        """Get download status for user's brokers"""
        try:
            if broker_name:
                download_key = f"{user_id}_{broker_name}"
                return download_status.get(download_key, {
                    'status': 'not_started',
                    'broker': broker_name
                })
            else:
                # Get status for all user's brokers
                user_status = {}
                broker_configs = get_user_broker_configs(user_id)
                
                for config in broker_configs:
                    broker_name = config['broker_name']
                    download_key = f"{user_id}_{broker_name}"
                    user_status[broker_name] = download_status.get(download_key, {
                        'status': 'not_started',
                        'broker': broker_name,
                        'symbol_count': get_broker_symbol_count(broker_name)
                    })
                
                return user_status
                
        except Exception as e:
            logger.error(f"Error getting download status: {e}")
            return {}

# Global instance
download_manager = MasterContractDownloadManager()

def trigger_automated_downloads_on_login(user_id):
    """
    Trigger automated master contract downloads when user logs in
    This should be called from the login success handler
    """
    try:
        logger.info(f"Triggering automated master contract downloads for user {user_id}")
        result = download_manager.start_automated_downloads(user_id)
        
        # Store result in session for user notification
        session['master_contract_download_status'] = result
        
        return result
        
    except Exception as e:
        logger.error(f"Error triggering automated downloads: {e}")
        return {
            'status': 'error',
            'message': f'Failed to trigger downloads: {str(e)}'
        }

def get_user_download_status(user_id):
    """Get download status for user's brokers"""
    return download_manager.get_download_status(user_id)

def get_broker_download_status(user_id, broker_name):
    """Get download status for specific broker"""
    return download_manager.get_download_status(user_id, broker_name)

def force_download_for_broker(user_id, broker_name):
    """Force download for a specific broker (ignoring date checks)"""
    try:
        download_key = f"{user_id}_{broker_name}"
        
        # Check if download is already in progress
        if download_key in download_threads and download_threads[download_key].is_alive():
            return {
                'status': 'error',
                'message': 'Download already in progress for this broker'
            }
        
        # Start download thread
        thread = threading.Thread(
            target=download_manager.download_master_contract_background,
            args=(user_id, broker_name),
            daemon=True
        )
        thread.start()
        download_threads[download_key] = thread
        
        return {
            'status': 'success',
            'message': f'Started download for {broker_name}'
        }
        
    except Exception as e:
        logger.error(f"Error forcing download for {broker_name}: {e}")
        return {
            'status': 'error',
            'message': f'Failed to start download: {str(e)}'
        }
