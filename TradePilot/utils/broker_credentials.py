# utils/broker_credentials.py

import os
import logging
from flask import session
from database.broker_config_db import get_broker_config

logger = logging.getLogger(__name__)

class BrokerCredentialProvider:
    """
    Dynamic credential provider that injects broker-specific credentials
    into environment variables temporarily for authentication
    """
    
    def __init__(self):
        self.original_env = {}
        self.is_injected = False
    
    def inject_credentials(self, broker_name, user_id=None):
        """
        Inject broker-specific credentials into environment variables
        """
        try:
            # Get user_id from session if not provided
            if not user_id:
                user_id = session.get('user_id') or session.get('user')
            
            if not user_id:
                logger.warning("No user_id available for credential injection")
                return False
            
            # Get broker configuration
            broker_config = get_broker_config(user_id, broker_name)
            
            if not broker_config:
                logger.warning(f"No configuration found for broker {broker_name} and user {user_id}")
                # Check if credentials exist in .env for backward compatibility
                if os.getenv('BROKER_API_KEY') and os.getenv('BROKER_API_SECRET'):
                    logger.info(f"Using .env credentials for {broker_name} (backward compatibility)")
                    return True
                return False
            
            # Store original environment variables
            self.original_env = {
                'BROKER_API_KEY': os.getenv('BROKER_API_KEY'),
                'BROKER_API_SECRET': os.getenv('BROKER_API_SECRET')
            }
            
            # Inject broker-specific credentials
            os.environ['BROKER_API_KEY'] = broker_config['api_key']
            os.environ['BROKER_API_SECRET'] = broker_config['api_secret']
            
            # Handle additional broker-specific configurations
            if broker_config.get('additional_config'):
                try:
                    import json
                    additional_config = json.loads(broker_config['additional_config']) if isinstance(broker_config['additional_config'], str) else broker_config['additional_config']
                    
                    # Map additional config to environment variables
                    if isinstance(additional_config, dict):
                        for key, value in additional_config.items():
                            if key in ['app_id', 'client_id', 'consumer_key']:
                                # Store original and inject new
                                if 'BROKER_API_KEY' not in self.original_env:
                                    self.original_env['BROKER_API_KEY'] = os.getenv('BROKER_API_KEY')
                                os.environ['BROKER_API_KEY'] = value
                            elif key in ['secret_key', 'access_token', 'consumer_secret']:
                                # Store original and inject new
                                if 'BROKER_API_SECRET' not in self.original_env:
                                    self.original_env['BROKER_API_SECRET'] = os.getenv('BROKER_API_SECRET')
                                os.environ['BROKER_API_SECRET'] = value
                            elif key == 'redirect_uri':
                                self.original_env['REDIRECT_URL'] = os.getenv('REDIRECT_URL')
                                os.environ['REDIRECT_URL'] = value
                            elif key in ['user_id', 'password', 'user_key', 'encryption_key', 'app_name', 'app_source']:
                                # Store additional broker-specific env vars
                                env_key = f'BROKER_{key.upper()}'
                                self.original_env[env_key] = os.getenv(env_key)
                                os.environ[env_key] = value
                except Exception as e:
                    logger.error(f"Error processing additional config for {broker_name}: {e}")
            
            self.is_injected = True
            logger.info(f"Injected credentials for broker {broker_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error injecting credentials for {broker_name}: {e}")
            return False
    
    def restore_credentials(self):
        """
        Restore original environment variables
        """
        try:
            if not self.is_injected:
                return
            
            # Restore original environment variables
            for key, value in self.original_env.items():
                if value is not None:
                    os.environ[key] = value
                else:
                    # Remove the key if it didn't exist originally
                    os.environ.pop(key, None)
            
            self.original_env = {}
            self.is_injected = False
            logger.debug("Restored original credentials")
            
        except Exception as e:
            logger.error(f"Error restoring credentials: {e}")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - automatically restore credentials"""
        self.restore_credentials()

# Global credential provider instance
_credential_provider = BrokerCredentialProvider()

def inject_broker_credentials(broker_name, user_id=None):
    """
    Convenience function to inject broker credentials
    """
    return _credential_provider.inject_credentials(broker_name, user_id)

def restore_broker_credentials():
    """
    Convenience function to restore original credentials
    """
    _credential_provider.restore_credentials()

def with_broker_credentials(broker_name, user_id=None):
    """
    Context manager for temporary credential injection
    
    Usage:
    with with_broker_credentials('dhan', user_id):
        # Broker authentication code here
        auth_token = authenticate_broker(code)
    """
    class CredentialContext:
        def __init__(self, broker_name, user_id):
            self.broker_name = broker_name
            self.user_id = user_id
            self.provider = BrokerCredentialProvider()
        
        def __enter__(self):
            success = self.provider.inject_credentials(self.broker_name, self.user_id)
            if not success:
                raise ValueError(f"Failed to inject credentials for broker {self.broker_name}")
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            self.provider.restore_credentials()
    
    return CredentialContext(broker_name, user_id)

def get_broker_credentials_for_auth(broker_name, user_id=None):
    """
    Get broker credentials for authentication without injecting into environment
    Returns: (api_key, api_secret, additional_config)
    """
    try:
        # Get user_id from session if not provided
        if not user_id:
            user_id = session.get('user_id') or session.get('user')
        
        if not user_id:
            logger.warning("No user_id available for getting credentials")
            return None, None, None
        
        # Get broker configuration
        broker_config = get_broker_config(user_id, broker_name)
        
        if broker_config:
            return (
                broker_config['api_key'], 
                broker_config['api_secret'], 
                broker_config.get('additional_config')
            )
        
        # Fallback to environment variables for backward compatibility
        api_key = os.getenv('BROKER_API_KEY')
        api_secret = os.getenv('BROKER_API_SECRET')
        
        if api_key and api_secret:
            logger.info(f"Using .env credentials for {broker_name} (backward compatibility)")
            return api_key, api_secret, None
        
        return None, None, None
        
    except Exception as e:
        logger.error(f"Error getting credentials for {broker_name}: {e}")
        return None, None, None
