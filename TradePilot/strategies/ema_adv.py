from openalgo import api
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta

api_key = '897f936c566ba22ab058b5dc799f956b64103ba94df64e799b08be6ab66613c5'
strategy = "EMA Crossover Python with SL"
symbol = "BHEL"
exchange = "NSE"
product = "MIS"
quantity = 1

fast_period = 5
slow_period = 10

client = api(api_key=api_key, host='http://127.0.0.1:5000')

# Risk management parameters
STOP_LOSS_PERCENT = 0.01   # 1% stop loss
TAKE_PROFIT_PERCENT = 0.02 # 2% take profit (optional)
TRAILING_STOP_PERCENT = 0.005  # 0.5% trailing stop loss

def calculate_ema_signals(df):
    close = df['close']
    ema_fast = close.ewm(span=fast_period, adjust=False).mean()
    ema_slow = close.ewm(span=slow_period, adjust=False).mean()

    prev_fast = ema_fast.shift(1)
    prev_slow = ema_slow.shift(1)
    curr_fast = ema_fast
    curr_slow = ema_slow

    crossover = (prev_fast < prev_slow) & (curr_fast > curr_slow)
    crossunder = (prev_fast > prev_slow) & (curr_fast < curr_slow)

    return pd.DataFrame({
        'EMA_Fast': ema_fast,
        'EMA_Slow': ema_slow,
        'Crossover': crossover,
        'Crossunder': crossunder
    }, index=df.index)

def ema_strategy():
    position = 0
    entry_price = None
    trailing_stop_price = None

    while True:
        try:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")

            df = client.history(
                symbol=symbol,
                exchange=exchange,
                interval="1m",
                start_date=start_date,
                end_date=end_date
            )

            if df.empty or 'close' not in df.columns:
                print("No data or missing close column. Retrying...")
                time.sleep(15)
                continue

            df['close'] = df['close'].round(2)
            signals = calculate_ema_signals(df)

            # Use second last candle to avoid partial candle
            crossover = signals['Crossover'].iloc[-2]
            crossunder = signals['Crossunder'].iloc[-2]
            current_price = df['close'].iloc[-1]

            # Check for stop loss or take profit hit if in position
            if position != 0 and entry_price is not None:
                # Calculate stop loss and take profit levels
                if position > 0:  # Long position
                    stop_loss_level = entry_price * (1 - STOP_LOSS_PERCENT)
                    take_profit_level = entry_price * (1 + TAKE_PROFIT_PERCENT)

                    # Update trailing stop
                    if trailing_stop_price is None or current_price > trailing_stop_price:
                        trailing_stop_price = max(trailing_stop_price or 0, current_price * (1 - TRAILING_STOP_PERCENT))

                    # Check if stop loss or trailing stop hit
                    if current_price <= stop_loss_level:
                        print(f"Stop loss hit for LONG at price {current_price:.2f}")
                        position = 0
                        entry_price = None
                        trailing_stop_price = None
                        response = client.placesmartorder(
                            strategy=strategy,
                            symbol=symbol,
                            action="SELL",
                            exchange=exchange,
                            price_type="MARKET",
                            product=product,
                            quantity=quantity,
                            position_size=position
                        )
                        print("Stop Loss Sell Order Response:", response)
                        time.sleep(15)
                        continue
                    elif current_price <= trailing_stop_price:
                        print(f"Trailing stop hit for LONG at price {current_price:.2f}")
                        position = 0
                        entry_price = None
                        trailing_stop_price = None
                        response = client.placesmartorder(
                            strategy=strategy,
                            symbol=symbol,
                            action="SELL",
                            exchange=exchange,
                            price_type="MARKET",
                            product=product,
                            quantity=quantity,
                            position_size=position
                        )
                        print("Trailing Stop Sell Order Response:", response)
                        time.sleep(15)
                        continue
                    elif current_price >= take_profit_level:
                        print(f"Take profit hit for LONG at price {current_price:.2f}")
                        position = 0
                        entry_price = None
                        trailing_stop_price = None
                        response = client.placesmartorder(
                            strategy=strategy,
                            symbol=symbol,
                            action="SELL",
                            exchange=exchange,
                            price_type="MARKET",
                            product=product,
                            quantity=quantity,
                            position_size=position
                        )
                        print("Take Profit Sell Order Response:", response)
                        time.sleep(15)
                        continue

                elif position < 0:  # Short position
                    stop_loss_level = entry_price * (1 + STOP_LOSS_PERCENT)
                    take_profit_level = entry_price * (1 - TAKE_PROFIT_PERCENT)

                    # Update trailing stop
                    if trailing_stop_price is None or current_price < trailing_stop_price:
                        trailing_stop_price = min(trailing_stop_price or float('inf'), current_price * (1 + TRAILING_STOP_PERCENT))

                    if current_price >= stop_loss_level:
                        print(f"Stop loss hit for SHORT at price {current_price:.2f}")
                        position = 0
                        entry_price = None
                        trailing_stop_price = None
                        response = client.placesmartorder(
                            strategy=strategy,
                            symbol=symbol,
                            action="BUY",
                            exchange=exchange,
                            price_type="MARKET",
                            product=product,
                            quantity=quantity,
                            position_size=position
                        )
                        print("Stop Loss Buy Order Response:", response)
                        time.sleep(15)
                        continue
                    elif current_price >= trailing_stop_price:
                        print(f"Trailing stop hit for SHORT at price {current_price:.2f}")
                        position = 0
                        entry_price = None
                        trailing_stop_price = None
                        response = client.placesmartorder(
                            strategy=strategy,
                            symbol=symbol,
                            action="BUY",
                            exchange=exchange,
                            price_type="MARKET",
                            product=product,
                            quantity=quantity,
                            position_size=position
                        )
                        print("Trailing Stop Buy Order Response:", response)
                        time.sleep(15)
                        continue
                    elif current_price <= take_profit_level:
                        print(f"Take profit hit for SHORT at price {current_price:.2f}")
                        position = 0
                        entry_price = None
                        trailing_stop_price = None
                        response = client.placesmartorder(
                            strategy=strategy,
                            symbol=symbol,
                            action="BUY",
                            exchange=exchange,
                            price_type="MARKET",
                            product=product,
                            quantity=quantity,
                            position_size=position
                        )
                        print("Take Profit Buy Order Response:", response)
                        time.sleep(15)
                        continue

            # Entry signals
            if crossover and position <= 0:
                position = quantity
                entry_price = current_price
                trailing_stop_price = None
                response = client.placesmartorder(
                    strategy=strategy,
                    symbol=symbol,
                    action="BUY",
                    exchange=exchange,
                    price_type="MARKET",
                    product=product,
                    quantity=quantity,
                    position_size=position
                )
                print("Buy Order Response:", response)

            elif crossunder and position >= 0:
                position = -quantity
                entry_price = current_price
                trailing_stop_price = None
                response = client.placesmartorder(
                    strategy=strategy,
                    symbol=symbol,
                    action="SELL",
                    exchange=exchange,
                    price_type="MARKET",
                    product=product,
                    quantity=quantity,
                    position_size=position
                )
                print("Sell Order Response:", response)

            print("\nStrategy Status:")
            print("-" * 50)
            print(f"Position: {position}")
            print(f"Entry Price: {entry_price}")
            print(f"Trailing Stop Price: {trailing_stop_price}")
            print(f"LTP: {current_price}")
            print(f"Fast EMA ({fast_period}): {signals['EMA_Fast'].iloc[-2]:.2f}")
            print(f"Slow EMA ({slow_period}): {signals['EMA_Slow'].iloc[-2]:.2f}")
            print(f"Buy Signal: {crossover}")
            print(f"Sell Signal: {crossunder}")
            print("-" * 50)

        except Exception as e:
            print(f"Error in strategy: {str(e)}")
            time.sleep(15)
            continue

        time.sleep(15)

if __name__ == "__main__":
    print(f"Starting {fast_period}/{slow_period} EMA Crossover Strategy with SL...")
    ema_strategy()
