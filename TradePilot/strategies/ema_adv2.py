import dash
from dash import dcc, html
from dash.dependencies import Input, Output
import plotly.graph_objs as go
import pandas as pd
from datetime import datetime
import threading
import time

# Global variable to hold your live data
live_df = pd.DataFrame(columns=['datetime', 'open', 'high', 'low', 'close', 'EMA_Fast', 'EMA_Slow', 'Buy', 'Sell'])

# Dummy data update function (replace with your real data update logic)
def update_live_data():
    global live_df
    while True:
        now = datetime.now()
        new_row = {
            'datetime': now,
            'open': 100,
            'high': 102,
            'low': 99,
            'close': 101,
            'EMA_Fast': 100.5,
            'EMA_Slow': 100.8,
            'Buy': now if now.second % 30 == 0 else None,
            'Sell': now if now.second % 45 == 0 else None,
        }
        live_df = live_df.append(new_row, ignore_index=True)
        # Keep only last 100 rows to avoid clutter
        live_df = live_df.tail(100)
        time.sleep(15)

# Start the data updating thread
threading.Thread(target=update_live_data, daemon=True).start()

app = dash.Dash(__name__)

app.layout = html.Div([
    html.H3("Live EMA Crossover Strategy"),
    dcc.Graph(id='live-graph', style={'height': '70vh'}),
    dcc.Interval(
        id='interval-component',
        interval=15*1000,  # Update every 15 seconds
        n_intervals=0
    )
])

@app.callback(
    Output('live-graph', 'figure'),
    Input('interval-component', 'n_intervals')
)
def update_graph_live(n):
    global live_df
    df = live_df.copy()
    if df.empty:
        return go.Figure()

    fig = go.Figure()

    # Candlestick
    fig.add_trace(go.Candlestick(
        x=df['datetime'],
        open=df['open'],
        high=df['high'],
        low=df['low'],
        close=df['close'],
        name='Price'
    ))

    # EMA lines
    fig.add_trace(go.Scatter(x=df['datetime'], y=df['EMA_Fast'], mode='lines', name='EMA Fast'))
    fig.add_trace(go.Scatter(x=df['datetime'], y=df['EMA_Slow'], mode='lines', name='EMA Slow'))

    # Buy signals
    buys = df.dropna(subset=['Buy'])
    fig.add_trace(go.Scatter(x=buys['datetime'], y=buys['close'], mode='markers', marker=dict(color='green', size=10), name='Buy Signal'))

    # Sell signals
    sells = df.dropna(subset=['Sell'])
    fig.add_trace(go.Scatter(x=sells['datetime'], y=sells['close'], mode='markers', marker=dict(color='red', size=10), name='Sell Signal'))

    fig.update_layout(xaxis_rangeslider_visible=False, template='plotly_dark')

    return fig

if __name__ == '__main__':
    app.run_server(debug=True)
