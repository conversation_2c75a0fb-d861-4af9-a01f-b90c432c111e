#!/usr/bin/env python3
"""
Database Migration Script for Multi-Broker Support
This script adds the new columns to existing auth table and creates new tables for multi-broker functionality.
"""

import sqlite3
import os
from datetime import datetime

def get_database_path():
    """Get the database path from environment or use default"""
    db_path = os.getenv('DATABASE_URL', 'sqlite:///db/openalgo.db')
    if db_path.startswith('sqlite:///'):
        return db_path[10:]  # Remove 'sqlite:///' prefix
    return 'db/openalgo.db'

def migrate_auth_table(cursor):
    """Add new columns to the auth table"""
    print("Migrating auth table...")
    
    # Check if columns already exist
    cursor.execute("PRAGMA table_info(auth)")
    columns = [column[1] for column in cursor.fetchall()]
    
    migrations = []
    
    if 'is_active' not in columns:
        migrations.append("ALTER TABLE auth ADD COLUMN is_active BOOLEAN DEFAULT 1")
        print("  - Adding is_active column")
    
    if 'priority' not in columns:
        migrations.append("ALTER TABLE auth ADD COLUMN priority INTEGER DEFAULT 1")
        print("  - Adding priority column")
    
    if 'created_at' not in columns:
        migrations.append("ALTER TABLE auth ADD COLUMN created_at DATETIME")
        print("  - Adding created_at column")

    if 'last_used' not in columns:
        migrations.append("ALTER TABLE auth ADD COLUMN last_used DATETIME")
        print("  - Adding last_used column")
    
    # Execute migrations
    for migration in migrations:
        cursor.execute(migration)
    
    # Remove unique constraint on name column if it exists
    # SQLite doesn't support dropping constraints directly, so we'll recreate the table
    if migrations:
        print("  - Updating table constraints...")
        # This is handled by the new table creation in the init_db function
    
    print("Auth table migration completed!")

def create_strategy_broker_mapping_table(cursor):
    """Create the strategy_broker_mappings table"""
    print("Creating strategy_broker_mappings table...")
    
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS strategy_broker_mappings (
            id INTEGER PRIMARY KEY,
            strategy_id INTEGER NOT NULL,
            broker VARCHAR(20) NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            priority INTEGER DEFAULT 1,
            allocation_percent REAL DEFAULT 100.0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (strategy_id) REFERENCES strategies (id),
            UNIQUE (strategy_id, broker)
        )
    """)
    print("Strategy broker mappings table created!")

def add_broker_column_to_symbol_mappings(cursor):
    """Add broker column to strategy_symbol_mappings table"""
    print("Updating strategy_symbol_mappings table...")
    
    # Check if table exists and if broker column exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='strategy_symbol_mappings'")
    if cursor.fetchone():
        cursor.execute("PRAGMA table_info(strategy_symbol_mappings)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'broker' not in columns:
            cursor.execute("ALTER TABLE strategy_symbol_mappings ADD COLUMN broker VARCHAR(20)")
            print("  - Added broker column to strategy_symbol_mappings")
        else:
            print("  - Broker column already exists in strategy_symbol_mappings")
    else:
        print("  - strategy_symbol_mappings table doesn't exist yet (will be created by init_db)")

def update_existing_data(cursor):
    """Update existing data with default values"""
    print("Updating existing data...")
    
    # Set default values for existing auth records
    cursor.execute("UPDATE auth SET is_active = 1 WHERE is_active IS NULL")
    cursor.execute("UPDATE auth SET priority = 1 WHERE priority IS NULL")
    cursor.execute("UPDATE auth SET created_at = CURRENT_TIMESTAMP WHERE created_at IS NULL")
    cursor.execute("UPDATE auth SET last_used = CURRENT_TIMESTAMP WHERE last_used IS NULL")
    
    print("Existing data updated!")

def main():
    """Main migration function"""
    print("=" * 60)
    print("TradePilot Multi-Broker Database Migration")
    print("=" * 60)
    
    db_path = get_database_path()
    print(f"Database path: {db_path}")
    
    if not os.path.exists(db_path):
        print("Database doesn't exist yet. It will be created with the new schema.")
        return
    
    # Backup the database
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    print(f"Creating backup: {backup_path}")
    
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print("Backup created successfully!")
    except Exception as e:
        print(f"Warning: Could not create backup: {e}")
        response = input("Continue without backup? (y/N): ")
        if response.lower() != 'y':
            print("Migration cancelled.")
            return
    
    # Connect to database and run migrations
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Run migrations
        migrate_auth_table(cursor)
        create_strategy_broker_mapping_table(cursor)
        add_broker_column_to_symbol_mappings(cursor)
        update_existing_data(cursor)
        
        # Commit changes
        conn.commit()
        print("\n" + "=" * 60)
        print("Migration completed successfully!")
        print("=" * 60)
        print("\nYou can now restart TradePilot to use the multi-broker features.")
        
    except Exception as e:
        print(f"\nError during migration: {e}")
        print("Rolling back changes...")
        conn.rollback()
        
        # Restore backup if it exists
        if os.path.exists(backup_path):
            try:
                import shutil
                shutil.copy2(backup_path, db_path)
                print("Database restored from backup.")
            except Exception as restore_error:
                print(f"Error restoring backup: {restore_error}")
        
        raise
    
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    main()
