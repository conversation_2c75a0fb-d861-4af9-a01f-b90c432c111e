#!/usr/bin/env python3
"""
Check database schema for auth table
"""

import sqlite3
import os
from dotenv import load_dotenv

load_dotenv()

def check_auth_schema():
    """Check the current auth table schema"""
    
    # Get database path from environment
    database_url = os.getenv('DATABASE_URL')
    if database_url.startswith('sqlite:///'):
        db_path = database_url[10:]  # Remove 'sqlite:///'
    else:
        print(f"Unsupported database URL: {database_url}")
        return
    
    print(f"Checking database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if auth table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='auth'")
        if not cursor.fetchone():
            print("❌ Auth table does not exist")
            return
        
        print("✅ Auth table exists")
        
        # Get table schema
        cursor.execute('PRAGMA table_info(auth)')
        columns = cursor.fetchall()
        
        print("\n📋 Auth table columns:")
        for col in columns:
            print(f"   {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - {'PRIMARY KEY' if col[5] else ''}")
        
        # Get table creation SQL
        cursor.execute('SELECT sql FROM sqlite_master WHERE type="table" AND name="auth"')
        creation_sql = cursor.fetchone()
        
        print(f"\n🔧 Auth table creation SQL:")
        print(creation_sql[0])
        
        # Check for unique constraints
        cursor.execute('PRAGMA index_list(auth)')
        indexes = cursor.fetchall()
        
        print(f"\n🔍 Indexes on auth table:")
        for index in indexes:
            index_name = index[1]
            is_unique = index[2]
            cursor.execute(f'PRAGMA index_info({index_name})')
            index_columns = cursor.fetchall()
            
            columns_str = ', '.join([col[2] for col in index_columns])
            unique_str = 'UNIQUE' if is_unique else 'NON-UNIQUE'
            print(f"   {index_name}: {unique_str} on ({columns_str})")
        
        # Check current data
        cursor.execute('SELECT name, broker, COUNT(*) FROM auth GROUP BY name, broker')
        data = cursor.fetchall()
        
        print(f"\n📊 Current auth data:")
        for row in data:
            print(f"   User: {row[0]}, Broker: {row[1]}, Count: {row[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")

if __name__ == "__main__":
    check_auth_schema()
