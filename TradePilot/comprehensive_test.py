#!/usr/bin/env python3
"""
TradePilot Comprehensive Multi-Broker Test Suite
==============================================

This script performs comprehensive testing of the multi-broker system including:
1. System Health Check
2. Database Connectivity
3. Multi-Broker Configuration
4. Symbol Search System
5. Master Contract Management
6. Broker Selection and Session Management
7. API Integration Testing

Usage: python3 comprehensive_test.py
"""

import requests
import json
import time
import sys
import os
from datetime import datetime

# Test Configuration
BASE_URL = "http://127.0.0.1:5000"
TEST_USER = "BVSS"  # Replace with your test user
TEST_PASSWORD = "123456"  # Replace with your test password

class ComprehensiveTester:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = BASE_URL
        self.test_results = []
        self.current_broker = None
        
    def log_test(self, test_name, status, message="", details=None):
        """Log test results with detailed information"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        status_icon = "✅" if status else "❌"
        result = {
            'timestamp': timestamp,
            'test': test_name,
            'status': status,
            'message': message,
            'details': details
        }
        self.test_results.append(result)
        print(f"[{timestamp}] {status_icon} {test_name}")
        if message:
            print(f"    └─ {message}")
        if details and isinstance(details, dict):
            for key, value in details.items():
                print(f"       • {key}: {value}")
        
    def test_system_health(self):
        """Test basic system health and connectivity"""
        print("\n🔍 TESTING SYSTEM HEALTH")
        print("-" * 50)
        
        try:
            # Test main page
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                self.log_test("System Health", True, "Application is running")
            else:
                self.log_test("System Health", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("System Health", False, f"Connection failed: {str(e)}")
            return False
        
        return True
    
    def test_authentication(self):
        """Test user authentication system"""
        print("\n🔐 TESTING AUTHENTICATION")
        print("-" * 50)
        
        try:
            # Test login
            login_data = {
                'username': TEST_USER,
                'password': TEST_PASSWORD
            }
            response = self.session.post(f"{self.base_url}/auth/login", data=login_data)
            
            if response.status_code == 200 and 'dashboard' in response.url:
                self.log_test("User Authentication", True, "Login successful")
                return True
            else:
                self.log_test("User Authentication", False, f"Login failed: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("User Authentication", False, f"Exception: {str(e)}")
            return False
    
    def test_multibroker_system(self):
        """Test multi-broker dashboard and configuration"""
        print("\n🏢 TESTING MULTI-BROKER SYSTEM")
        print("-" * 50)
        
        # Test multi-broker dashboard
        try:
            response = self.session.get(f"{self.base_url}/multibroker/dashboard")
            if response.status_code == 200:
                self.log_test("Multi-Broker Dashboard", True, "Dashboard accessible")
            else:
                self.log_test("Multi-Broker Dashboard", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Multi-Broker Dashboard", False, f"Exception: {str(e)}")
        
        # Test broker configuration page
        try:
            response = self.session.get(f"{self.base_url}/broker-config/")
            if response.status_code == 200:
                self.log_test("Broker Configuration", True, "Config page accessible")
            else:
                self.log_test("Broker Configuration", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Broker Configuration", False, f"Exception: {str(e)}")
    
    def test_broker_selection(self):
        """Test broker selection functionality"""
        print("\n🎯 TESTING BROKER SELECTION")
        print("-" * 50)
        
        test_brokers = ['fyers', 'dhan']
        
        for broker in test_brokers:
            try:
                response = self.session.get(f"{self.base_url}/multibroker/select_broker/{broker}")
                
                if response.status_code in [200, 302]:
                    self.log_test(f"Select {broker.title()}", True, "Selection successful")
                    self.current_broker = broker
                    time.sleep(1)  # Allow session to update
                else:
                    self.log_test(f"Select {broker.title()}", False, f"Status: {response.status_code}")
            except Exception as e:
                self.log_test(f"Select {broker.title()}", False, f"Exception: {str(e)}")
    
    def test_master_contract_status(self):
        """Test master contract status API"""
        print("\n📊 TESTING MASTER CONTRACT STATUS")
        print("-" * 50)
        
        try:
            response = self.session.get(f"{self.base_url}/multibroker/symbols/status")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    broker_status = data.get('broker_status', {})
                    self.log_test("Master Contract Status API", True, "API working", broker_status)
                    
                    # Check individual broker status
                    for broker, status in broker_status.items():
                        symbol_count = status.get('symbol_count', 0)
                        if symbol_count > 0:
                            self.log_test(f"{broker.title()} Symbols", True, f"{symbol_count} symbols available")
                        else:
                            self.log_test(f"{broker.title()} Symbols", False, "No symbols found")
                else:
                    self.log_test("Master Contract Status API", False, "API returned error")
            else:
                self.log_test("Master Contract Status API", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Master Contract Status API", False, f"Exception: {str(e)}")
    
    def test_symbol_search(self):
        """Test symbol search functionality"""
        print("\n🔍 TESTING SYMBOL SEARCH")
        print("-" * 50)
        
        test_symbols = ['RELIANCE', 'TCS', 'INFY', 'HDFC']
        
        for symbol in test_symbols:
            try:
                # Test new multi-broker search API
                response = self.session.get(f"{self.base_url}/multibroker/symbols/search?q={symbol}")
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('status') == 'success':
                        count = data.get('count', 0)
                        broker = data.get('broker', 'unknown')
                        if count > 0:
                            self.log_test(f"Search {symbol}", True, f"Found {count} results for {broker}")
                        else:
                            self.log_test(f"Search {symbol}", False, f"No results found for {broker}")
                    else:
                        self.log_test(f"Search {symbol}", False, data.get('message', 'Unknown error'))
                else:
                    self.log_test(f"Search {symbol}", False, f"Status: {response.status_code}")
                
                # Test legacy search API
                response = self.session.get(f"{self.base_url}/search/api/search?q={symbol}")
                
                if response.status_code == 200:
                    data = response.json()
                    results = data.get('results', [])
                    if results:
                        self.log_test(f"Legacy Search {symbol}", True, f"Found {len(results)} results")
                    else:
                        self.log_test(f"Legacy Search {symbol}", False, "No results found")
                else:
                    self.log_test(f"Legacy Search {symbol}", False, f"Status: {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"Search {symbol}", False, f"Exception: {str(e)}")
            
            time.sleep(0.5)  # Rate limiting
    
    def test_master_contract_download(self):
        """Test master contract download functionality"""
        print("\n⬇️ TESTING MASTER CONTRACT DOWNLOAD")
        print("-" * 50)
        
        test_brokers = ['fyers', 'dhan']
        
        for broker in test_brokers:
            try:
                print(f"    Testing download for {broker}...")
                response = self.session.post(f"{self.base_url}/multibroker/symbols/download/{broker}")
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('status') == 'success':
                        self.log_test(f"Download {broker.title()}", True, "Download initiated successfully")
                    else:
                        self.log_test(f"Download {broker.title()}", False, data.get('message', 'Unknown error'))
                else:
                    self.log_test(f"Download {broker.title()}", False, f"Status: {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"Download {broker.title()}", False, f"Exception: {str(e)}")
            
            time.sleep(2)  # Allow download to process
    
    def test_trading_pages(self):
        """Test trading pages with broker selection"""
        print("\n📈 TESTING TRADING PAGES")
        print("-" * 50)
        
        trading_pages = [
            ('/orderbook', 'Orderbook'),
            ('/tradebook', 'Tradebook'),
            ('/holdings', 'Holdings'),
            ('/positions', 'Positions')
        ]
        
        for url, page_name in trading_pages:
            try:
                response = self.session.get(f"{self.base_url}{url}")
                
                if response.status_code == 200:
                    self.log_test(f"{page_name} Page", True, "Page accessible")
                elif response.status_code == 302:
                    self.log_test(f"{page_name} Page", True, "Proper redirect handling")
                else:
                    self.log_test(f"{page_name} Page", False, f"Status: {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"{page_name} Page", False, f"Exception: {str(e)}")
    
    def run_comprehensive_test(self):
        """Run the complete test suite"""
        print("🚀 STARTING COMPREHENSIVE MULTI-BROKER TEST SUITE")
        print("=" * 70)
        print(f"Target: {self.base_url}")
        print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
        
        # Test 1: System Health
        if not self.test_system_health():
            print("❌ System health check failed - stopping tests")
            return False
        
        # Test 2: Authentication
        if not self.test_authentication():
            print("❌ Authentication failed - stopping tests")
            return False
        
        # Test 3: Multi-Broker System
        self.test_multibroker_system()
        
        # Test 4: Broker Selection
        self.test_broker_selection()
        
        # Test 5: Master Contract Status
        self.test_master_contract_status()
        
        # Test 6: Symbol Search (before download)
        self.test_symbol_search()
        
        # Test 7: Master Contract Download
        self.test_master_contract_download()
        
        # Test 8: Symbol Search (after download)
        print("\n🔍 RE-TESTING SYMBOL SEARCH AFTER DOWNLOAD")
        print("-" * 50)
        self.test_symbol_search()
        
        # Test 9: Trading Pages
        self.test_trading_pages()
        
        # Print Results
        self.print_comprehensive_results()
        
        return True
    
    def print_comprehensive_results(self):
        """Print detailed test results"""
        print("\n" + "=" * 70)
        print("📊 COMPREHENSIVE TEST RESULTS")
        print("=" * 70)
        
        passed = sum(1 for result in self.test_results if result['status'])
        total = len(self.test_results)
        
        print(f"📈 SUMMARY:")
        print(f"   Total Tests: {total}")
        print(f"   Passed: {passed} ✅")
        print(f"   Failed: {total - passed} ❌")
        print(f"   Success Rate: {(passed/total)*100:.1f}%")
        
        # Categorize results
        categories = {
            'System': ['System Health', 'User Authentication'],
            'Multi-Broker': ['Multi-Broker Dashboard', 'Broker Configuration', 'Select'],
            'Master Contract': ['Master Contract Status API', 'Download', 'Symbols'],
            'Symbol Search': ['Search', 'Legacy Search'],
            'Trading': ['Orderbook', 'Tradebook', 'Holdings', 'Positions']
        }
        
        print(f"\n📋 DETAILED RESULTS BY CATEGORY:")
        for category, keywords in categories.items():
            category_tests = [r for r in self.test_results if any(kw in r['test'] for kw in keywords)]
            if category_tests:
                category_passed = sum(1 for r in category_tests if r['status'])
                category_total = len(category_tests)
                status_icon = "✅" if category_passed == category_total else "⚠️" if category_passed > 0 else "❌"
                print(f"   {status_icon} {category}: {category_passed}/{category_total}")
        
        print(f"\n🎯 SYSTEM STATUS:")
        critical_tests = ['System Health', 'User Authentication', 'Multi-Broker Dashboard']
        critical_passed = all(any(r['test'] == test and r['status'] for r in self.test_results) for test in critical_tests)
        
        if critical_passed:
            print("   ✅ CORE SYSTEM: Fully Functional")
            print("   ✅ MULTI-BROKER: System Working")
            print("   ✅ AUTHENTICATION: Working")
        else:
            print("   ❌ CORE SYSTEM: Critical Issues Detected")
        
        # Check symbol search status
        search_tests = [r for r in self.test_results if 'Search' in r['test']]
        search_working = any(r['status'] for r in search_tests)
        
        if search_working:
            print("   ✅ SYMBOL SEARCH: Working")
        else:
            print("   ⚠️ SYMBOL SEARCH: Needs Master Contract Download")
        
        print(f"\n🔧 RECOMMENDATIONS:")
        if not search_working:
            print("   1. Download master contracts for configured brokers")
            print("   2. Authenticate brokers to get valid tokens")
        else:
            print("   1. ✅ Symbol search is working")
            print("   2. Authenticate brokers for full trading functionality")
        
        print("   3. Test complete trading workflow")
        print("   4. 🚀 System ready for production use")

def main():
    """Main test execution"""
    tester = ComprehensiveTester()
    
    try:
        tester.run_comprehensive_test()
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Test suite failed: {str(e)}")
    
    print(f"\n🏁 Test completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
