#!/usr/bin/env python3
"""
Test script to debug authentication flow issues
"""

import os
import sys
sys.path.append('.')

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

from database.auth_db import get_broker_auth, get_all_user_brokers, upsert_auth
from database.broker_config_db import get_broker_config, get_user_broker_configs

def test_auth_flow():
    """Test the authentication flow for debugging"""
    
    print("🔍 TESTING AUTHENTICATION FLOW")
    print("=" * 50)
    
    user_id = 1  # Test user
    
    # Test 1: Check existing broker configs
    print("\n1. Checking broker configurations:")
    try:
        configs = get_user_broker_configs(user_id)
        print(f"   Found {len(configs)} broker configs:")
        for config in configs:
            print(f"   - {config['broker_name']}: {config['api_key'][:10]}...")
    except Exception as e:
        print(f"   ❌ Error getting configs: {e}")
    
    # Test 2: Check existing auth tokens
    print("\n2. Checking existing auth tokens:")
    try:
        brokers = get_all_user_brokers(user_id)
        print(f"   Found {len(brokers)} authenticated brokers:")
        for broker in brokers:
            print(f"   - {broker['broker']}: {broker['auth_token'][:10] if broker['auth_token'] else 'None'}...")
    except Exception as e:
        print(f"   ❌ Error getting brokers: {e}")
    
    # Test 3: Check specific broker auth
    print("\n3. Checking specific broker auth:")
    for broker_name in ['fyers', 'dhan']:
        try:
            auth = get_broker_auth(user_id, broker_name)
            if auth:
                print(f"   - {broker_name}: ✅ Found auth token")
            else:
                print(f"   - {broker_name}: ❌ No auth token")
        except Exception as e:
            print(f"   - {broker_name}: ❌ Error: {e}")
    
    # Test 4: Test auth token storage
    print("\n4. Testing auth token storage:")
    test_token = "test_token_12345"
    try:
        result = upsert_auth(user_id, test_token, 'test_broker')
        if result:
            print(f"   ✅ Successfully stored test token with ID: {result}")
            
            # Verify retrieval
            auth = get_broker_auth(user_id, 'test_broker')
            if auth and auth['auth_token'] == test_token:
                print(f"   ✅ Successfully retrieved test token")
            else:
                print(f"   ❌ Failed to retrieve test token")
        else:
            print(f"   ❌ Failed to store test token")
    except Exception as e:
        print(f"   ❌ Error testing storage: {e}")

if __name__ == "__main__":
    test_auth_flow()
