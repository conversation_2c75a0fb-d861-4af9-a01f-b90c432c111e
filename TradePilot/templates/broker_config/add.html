{% extends "base.html" %}

{% block title %}{{ 'Edit' if existing_config else 'Add' }} {{ broker_info.display_name }} Configuration{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-building"></i> 
                        {{ 'Edit' if existing_config else 'Add' }} {{ broker_info.display_name }} Configuration
                    </h3>
                </div>
                
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>{{ broker_info.display_name }}</strong> - {{ broker_info.description }}
                    </div>
                    
                    <form id="brokerConfigForm">
                        <input type="hidden" name="broker_name" value="{{ broker_name }}">
                        
                        {% for field in broker_info.fields %}
                        <div class="mb-3">
                            <label for="{{ field }}" class="form-label">
                                {{ field.replace('_', ' ').title() }}
                                <span class="text-danger">*</span>
                            </label>
                            
                            {% if field in ['api_secret', 'secret_key', 'password', 'encryption_key', 'access_token'] %}
                            <input type="password" 
                                   class="form-control" 
                                   id="{{ field }}" 
                                   name="{{ field }}" 
                                   value="{{ existing_config.additional_config[field] if existing_config and existing_config.additional_config and field in existing_config.additional_config else '' }}"
                                   required>
                            {% elif field == 'redirect_uri' %}
                            <input type="url" 
                                   class="form-control" 
                                   id="{{ field }}" 
                                   name="{{ field }}" 
                                   value="{{ existing_config.additional_config[field] if existing_config and existing_config.additional_config and field in existing_config.additional_config else 'http://127.0.0.1:5000/auth/callback' }}"
                                   placeholder="http://127.0.0.1:5000/auth/callback"
                                   required>
                            {% else %}
                            <input type="text" 
                                   class="form-control" 
                                   id="{{ field }}" 
                                   name="{{ field }}" 
                                   value="{{ existing_config.additional_config[field] if existing_config and existing_config.additional_config and field in existing_config.additional_config else (existing_config.api_key if field == 'api_key' and existing_config else '') }}"
                                   required>
                            {% endif %}
                            
                            {% if field == 'api_key' %}
                            <div class="form-text">Your broker API key from the developer console</div>
                            {% elif field == 'api_secret' %}
                            <div class="form-text">Your broker API secret (keep this secure)</div>
                            {% elif field == 'redirect_uri' %}
                            <div class="form-text">OAuth redirect URL (use default unless you have a custom setup)</div>
                            {% elif field == 'app_id' %}
                            <div class="form-text">Your application ID from the broker</div>
                            {% elif field == 'client_id' %}
                            <div class="form-text">Your client ID from the broker</div>
                            {% elif field == 'user_id' %}
                            <div class="form-text">Your user ID with the broker</div>
                            {% elif field == 'password' %}
                            <div class="form-text">Your trading password</div>
                            {% elif field == 'user_key' %}
                            <div class="form-text">Your user key from the broker</div>
                            {% elif field == 'encryption_key' %}
                            <div class="form-text">Encryption key provided by the broker</div>
                            {% elif field == 'app_name' %}
                            <div class="form-text">Application name registered with the broker</div>
                            {% elif field == 'app_source' %}
                            <div class="form-text">Application source identifier</div>
                            {% elif field == 'consumer_key' %}
                            <div class="form-text">Consumer key from the broker</div>
                            {% elif field == 'consumer_secret' %}
                            <div class="form-text">Consumer secret from the broker</div>
                            {% elif field == 'access_token' %}
                            <div class="form-text">Access token from the broker</div>
                            {% endif %}
                        </div>
                        {% endfor %}
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-shield-alt"></i>
                            <strong>Security Notice:</strong> Your credentials are encrypted and stored securely. 
                            They are only used for API authentication with your broker.
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('broker_config_bp.list_configs') }}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> {{ 'Update' if existing_config else 'Save' }} Configuration
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('brokerConfigForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    submitBtn.disabled = true;
    
    fetch('/broker-config/save', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => {
                window.location.href = '/broker-config/';
            }, 1500);
        } else {
            showAlert('danger', data.message);
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    })
    .catch(error => {
        showAlert('danger', 'Error saving configuration');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.row'));
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
