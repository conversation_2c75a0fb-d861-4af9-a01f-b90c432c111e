{% extends "base.html" %}

{% block title %}Broker Configurations{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-cogs"></i> Broker Configurations
                    </h3>
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-plus"></i> Add Broker
                        </button>
                        <ul class="dropdown-menu">
                            {% for broker_key, broker_info in broker_configs.items() %}
                            <li>
                                <a class="dropdown-item" href="{{ url_for('broker_config_bp.add_config_form', broker_name=broker_key) }}">
                                    <i class="fas fa-building"></i> {{ broker_info.display_name }}
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                
                <div class="card-body">
                    {% if configs %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Broker</th>
                                    <th>Status</th>
                                    <th>Last Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for config in configs %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-building me-2"></i>
                                            <div>
                                                <strong>{{ broker_configs[config.broker_name].display_name if config.broker_name in broker_configs else config.broker_name.title() }}</strong>
                                                <br>
                                                <small class="text-muted">{{ broker_configs[config.broker_name].description if config.broker_name in broker_configs else 'API Configuration' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle"></i> Configured
                                        </span>
                                    </td>
                                    <td>
                                        <small>{{ config.updated_at.strftime('%Y-%m-%d %H:%M') if config.updated_at else 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('broker_config_bp.add_config_form', broker_name=config.broker_name) }}" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-success" 
                                                    onclick="testConfig('{{ config.broker_name }}')" title="Test">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-info"
                                                    onclick="downloadMasterContract('{{ config.broker_name }}')"
                                                    title="Download Master Contract"
                                                    id="download-btn-{{ config.broker_name }}">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteConfig('{{ config.broker_name }}')" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Broker Configurations</h4>
                        <p class="text-muted">Add your first broker configuration to start trading</p>
                        <div class="dropdown">
                            <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-plus"></i> Add Your First Broker
                            </button>
                            <ul class="dropdown-menu">
                                {% for broker_key, broker_info in broker_configs.items() %}
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('broker_config_bp.add_config_form', broker_name=broker_key) }}">
                                        <i class="fas fa-building"></i> {{ broker_info.display_name }}
                                    </a>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testConfig(brokerName) {
    fetch(`/broker-config/test/${brokerName}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            showAlert('danger', 'Error testing configuration');
        });
}

function deleteConfig(brokerName) {
    if (confirm(`Are you sure you want to delete the ${brokerName} configuration?`)) {
        fetch(`/broker-config/delete/${brokerName}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            showAlert('danger', 'Error deleting configuration');
        });
    }
}

function downloadMasterContract(brokerName) {
    const button = document.getElementById(`download-btn-${brokerName}`);
    const originalHtml = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    fetch(`/multibroker/symbols/download/${brokerName}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAlert('success', `Master contract downloaded successfully for ${brokerName}`);
        } else {
            showAlert('danger', data.message || `Failed to download master contract for ${brokerName}`);
        }
    })
    .catch(error => {
        showAlert('danger', `Error downloading master contract for ${brokerName}`);
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalHtml;
        button.disabled = false;
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.row'));

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
