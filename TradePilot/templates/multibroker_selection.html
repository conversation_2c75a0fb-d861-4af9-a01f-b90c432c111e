{% extends "base.html" %}

{% block title %}Multi-Broker Selection{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="text-center mb-8">
        <h1 class="text-4xl font-bold mb-4">Multi-Broker Trading Platform</h1>
        <p class="text-lg text-base-content/70">Connect multiple brokers to diversify your trading operations</p>
    </div>

    <!-- Existing Brokers Section -->
    {% if existing_brokers %}
    <div class="mb-8">
        <h2 class="text-2xl font-semibold mb-4">Connected Brokers</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            {% for broker in existing_brokers %}
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="card-title">{{ broker.broker|title }}</h3>
                            <div class="badge badge-success badge-sm">Connected</div>
                        </div>
                        <div class="dropdown dropdown-end">
                            <label tabindex="0" class="btn btn-ghost btn-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                </svg>
                            </label>
                            <ul tabindex="0" class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52">
                                <li><a href="/multibroker/broker/{{ broker.broker }}">View Dashboard</a></li>
                                <li><a onclick="reconnectBroker('{{ broker.broker }}')">Reconnect</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="text-sm text-base-content/70">Priority: {{ broker.priority }}</div>
                        <div class="text-sm text-base-content/70">Last Used: {{ broker.last_used.strftime('%Y-%m-%d %H:%M') if broker.last_used else 'Never' }}</div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Quick Actions for Existing Users -->
        <div class="flex gap-4 justify-center mb-8">
            <a href="/multibroker/dashboard" class="btn btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                Go to Dashboard
            </a>
            <button onclick="showAddBrokerModal()" class="btn btn-outline">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Another Broker
            </button>
        </div>
    </div>
    {% endif %}

    <!-- Available Brokers Section -->
    <div class="mb-8">
        <h2 class="text-2xl font-semibold mb-4">
            {% if existing_brokers %}Add New Broker{% else %}Connect Your First Broker{% endif %}
        </h2>
        
        <!-- Broker Grid -->
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {% for broker in available_brokers %}
            <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow cursor-pointer broker-card" 
                 data-broker="{{ broker }}" onclick="selectBroker('{{ broker }}')">
                <div class="card-body p-4 text-center">
                    <div class="w-12 h-12 mx-auto mb-2 bg-primary/10 rounded-lg flex items-center justify-center">
                        <span class="text-primary font-bold text-lg">{{ broker[0]|upper }}</span>
                    </div>
                    <h3 class="text-sm font-semibold">{{ broker|title }}</h3>
                    {% if broker in existing_brokers|map(attribute='broker')|list %}
                    <div class="badge badge-success badge-xs mt-1">Connected</div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Instructions -->
    <div class="alert alert-info">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div>
            <h3 class="font-bold">Multi-Broker Benefits</h3>
            <div class="text-sm">
                • <strong>Risk Distribution:</strong> Spread your trades across multiple brokers<br>
                • <strong>Better Execution:</strong> Choose the best broker for each strategy<br>
                • <strong>Redundancy:</strong> Continue trading even if one broker has issues<br>
                • <strong>Cost Optimization:</strong> Use different brokers for different market segments
            </div>
        </div>
    </div>
</div>

<!-- Broker Connection Modal -->
<div id="brokerModal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg">Connect to <span id="selectedBrokerName"></span></h3>
        <div class="py-4">
            <div class="alert alert-warning mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <span>You will be redirected to the broker's login page. Please have your credentials ready.</span>
            </div>
            
            <div class="form-control">
                <label class="label">
                    <span class="label-text">Set Priority (1-10, higher = preferred)</span>
                </label>
                <input type="number" id="brokerPriority" class="input input-bordered" min="1" max="10" value="1">
            </div>
        </div>
        <div class="modal-action">
            <button class="btn" onclick="closeBrokerModal()">Cancel</button>
            <button class="btn btn-primary" onclick="connectToBroker()">Connect</button>
        </div>
    </div>
</div>

<script>
let selectedBroker = null;

function selectBroker(broker) {
    selectedBroker = broker;
    document.getElementById('selectedBrokerName').textContent = broker.charAt(0).toUpperCase() + broker.slice(1);
    document.getElementById('brokerModal').classList.add('modal-open');
}

function closeBrokerModal() {
    document.getElementById('brokerModal').classList.remove('modal-open');
    selectedBroker = null;
}

function showAddBrokerModal() {
    // Show the broker grid if it's hidden
    const brokerGrid = document.querySelector('.grid');
    if (brokerGrid) {
        brokerGrid.scrollIntoView({ behavior: 'smooth' });
    }
}

function connectToBroker() {
    if (!selectedBroker) return;
    
    const priority = document.getElementById('brokerPriority').value;
    
    // Store priority in session storage for later use
    sessionStorage.setItem('brokerPriority', priority);
    
    // Redirect to broker connection
    window.location.href = `/${selectedBroker}/callback`;
}

function reconnectBroker(broker) {
    // Redirect to broker connection for reconnection
    window.location.href = `/${broker}/callback`;
}

// Add hover effects to broker cards
document.addEventListener('DOMContentLoaded', function() {
    const brokerCards = document.querySelectorAll('.broker-card');
    brokerCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('scale-105');
        });
        card.addEventListener('mouseleave', function() {
            this.classList.remove('scale-105');
        });
    });
});
</script>

<style>
.broker-card {
    transition: transform 0.2s ease-in-out;
}

.broker-card:hover {
    transform: scale(1.05);
}

.modal-open {
    display: flex !important;
}
</style>
{% endblock %}
