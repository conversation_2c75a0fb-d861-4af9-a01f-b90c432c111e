{% extends "base.html" %}

{% block head %}
<link href="{{ url_for('static', filename='css/prism.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='css/tradingview.css') }}" rel="stylesheet" />
<style>
.webhook-url {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold">TradingView Configuration</h1>
        <p class="text-base-content/60 mt-2">Generate webhook configuration for TradingView strategy alerts</p>
    </div>

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Left Column -->
        <div class="space-y-6">
            <!-- Webhook URL Card -->
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title text-primary">Webhook URL</h2>
                    <p class="text-base-content/60">Use this URL in your TradingView alerts</p>
                    
                    <div class="alert bg-base-200 mt-4">
                        <div class="flex-1 font-mono text-sm webhook-url tooltip" data-tip="{{host}}/api/v1/placesmartorder">
                            .../api/v1/placesmartorder
                        </div>
                        <button id="copy-webhook" class="btn btn-secondary btn-sm gap-2" onclick="copyWebhook('{{host}}/api/v1/placesmartorder')">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            Copy
                        </button>
                    </div>
                    <div class="h-6">
                        <span id="webhookCopyAlert" class="text-success text-sm hidden">✓ Copied to clipboard</span>
                    </div>
                </div>
            </div>

            <!-- Configuration Form Card -->
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title">Configuration</h2>
                    <form id="tradingview-form" class="space-y-4">
                        <!-- Symbol Search -->
                        <div class="form-control w-full">
                            <label class="label">
                                <span class="label-text font-medium">Symbol</span>
                            </label>
                            <div class="relative">
                                <input type="text" 
                                       id="symbol" 
                                       name="symbol" 
                                       required 
                                       class="input input-bordered w-full" 
                                       placeholder="Search for symbol..."
                                       autocomplete="off">
                                <div class="loading-indicator">
                                    <span class="loading loading-spinner loading-sm"></span>
                                </div>
                                <div id="searchResults" class="menu bg-base-200 w-full rounded-box absolute z-50 mt-1 hidden"></div>
                            </div>
                        </div>

                        <!-- Exchange Select -->
                        <div class="form-control w-full">
                            <label class="label">
                                <span class="label-text font-medium">Exchange</span>
                            </label>
                            <select id="exchange" name="exchange" required class="select select-bordered w-full">
                                <option value="">Select Exchange</option>
                                <option value="NSE">NSE</option>
                                <option value="NFO">NFO</option>
                                <option value="BSE">BSE</option>
                                <option value="BFO">BFO</option>
                                <option value="CDS">CDS</option>
                                <option value="MCX">MCX</option>
                            </select>
                        </div>

                        <!-- Product Type -->
                        <div class="form-control w-full">
                            <label class="label">
                                <span class="label-text font-medium">Product Type</span>
                            </label>
                            <select id="product" name="product" class="select select-bordered w-full">
                                <option value="MIS">MIS</option>
                                <option value="NRML">NRML</option>
                                <option value="CNC">CNC</option>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary w-full gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Generate JSON
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-6">
            <!-- JSON Output Card -->
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <div class="flex justify-between items-center">
                        <h2 class="card-title">Generated JSON</h2>
                        <button id="copy-json" class="btn btn-secondary btn-sm gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            Copy
                        </button>
                    </div>
                    <div class="bg-base-200 rounded-lg mt-4">
                        <pre><code id="json-output" class="language-json"></code></pre>
                    </div>
                    <div class="h-6">
                        <span id="copy-success-alert" class="text-success text-sm hidden">✓ Copied to clipboard</span>
                    </div>
                </div>
            </div>

            <!-- Documentation Card -->
            <div class="card bg-accent text-accent-content shadow-xl">
                <div class="card-body">
                    <h2 class="card-title">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                        Need Help?
                    </h2>
                    <p>Learn how to set up automated trading using TradingView webhooks with our step-by-step guide.</p>
                    <div class="card-actions justify-end">
                        <a href="https://docs.openalgo.in/trading-platform/tradingview" target="_blank" rel="noopener noreferrer" class="btn btn-primary gap-2">
                            View Documentation
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Prism JS and TradingView JS -->
<script src="{{ url_for('static', filename='js/prism.js') }}"></script>
<script src="{{ url_for('static', filename='js/tradingview.js') }}"></script>
<script>
function copyWebhook(url) {
    navigator.clipboard.writeText(url).then(() => {
        const alert = document.getElementById('webhookCopyAlert');
        alert.classList.remove('hidden');
        setTimeout(() => {
            alert.classList.add('hidden');
        }, 2000);
    });
}
</script>
{% endblock %}
