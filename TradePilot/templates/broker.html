{% extends "layout.html" %}

{% block title %}Trading Account Authentication - OpenAlgo{% endblock %}

{% block head %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to extract API key for Flattrade
        function getFlattradeApiKey(fullKey) {
            if (!fullKey) return '';
            const parts = fullKey.split(':::');
            return parts.length > 1 ? parts[1] : fullKey;
        }

        // Auto-select the enabled broker
        const select = document.getElementById('platform-select');
        const enabledOption = Array.from(select.options).find(option => !option.disabled);
        if (enabledOption) {
            enabledOption.selected = true;
        }

        // Form submission handler
        document.getElementById('trading-platform-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const platform = document.getElementById('platform-select').value;
            let loginUrl;

            switch(platform) {
                case 'fivepaisa':
                    loginUrl = '/fivepaisa/callback';
                    break;
                    case 'fivepaisaxts':
                    loginUrl = '/fivepaisaxts/callback';
                    break;
                case 'aliceblue':
                    loginUrl = '/aliceblue/callback';
                    break;
                case 'compositedge':
                    loginUrl = 'https://xts.compositedge.com/interactive/thirdparty?appKey={{broker_api_key}}&returnURL={{ redirect_url }}';
                    break;
                case 'angel':
                    loginUrl = '/angel/callback';
                    break;
                
                case 'dhan':
                    loginUrl = '/dhan/callback';
                    break;
                case 'dhan_sandbox':
                    loginUrl = '/dhan_sandbox/callback';
                    break;
                case 'firstock':
                    loginUrl = '/firstock/callback';
                    break;
                case 'flattrade':
                    const flattradeApiKey = getFlattradeApiKey('{{broker_api_key}}');
                    loginUrl = 'https://auth.flattrade.in/?app_key=' + flattradeApiKey;
                    break;
                case 'fyers':
                    loginUrl = 'https://api-t1.fyers.in/api/v3/generate-authcode?client_id={{broker_api_key}}&redirect_uri={{redirect_url}}&response_type=code&state=2e9b44629ebb28226224d09db3ffb47c';
                    break;
                case 'groww':
                    loginUrl = '/groww/callback';
                    break;
                case 'iifl':
                    loginUrl = '/iifl/callback';
                    break;
                case 'jainam':
                    loginUrl = '/jainam/callback';
                    break;
                case 'jainampro':
                    loginUrl = '/jainampro/callback';
                    break;
                case 'kotak':
                    loginUrl = '/kotak/callback';
                    break;
                case 'shoonya':
                    loginUrl = '/shoonya/callback';
                    break;
                case 'tradejini':
                    loginUrl = '/tradejini/callback';
                    break;
                case 'upstox':
                    loginUrl = 'https://api.upstox.com/v2/login/authorization/dialog?response_type=code&client_id={{broker_api_key}}&redirect_uri={{ redirect_url }}';
                    break;
                case 'wisdom':
                    loginUrl = '/wisdom/callback';
                    break;
                case 'zebu':
                    loginUrl = '/zebu/callback';
                    break;
                case 'zerodha':
                    loginUrl = 'https://kite.trade/connect/login?api_key={{broker_api_key}}';
                    break;
                case 'paytm':
                    loginUrl = 'https://login.paytmmoney.com/merchant-login?apiKey={{broker_api_key}}&state={default}';
                    break;
                case 'pocketful':
                    // Generate a random state for security
                    function generateRandomState() {
                        const length = 16;
                        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                        let result = '';
                        for (let i = 0; i < length; i++) {
                            result += chars.charAt(Math.floor(Math.random() * chars.length));
                        }
                        return result;
                    }
                    const state = generateRandomState();
                    // Store state for verification when callback returns
                    localStorage.setItem('pocketful_oauth_state', state);
                    // Define the scope for permissions
                    const scope = "orders holdings";
                    // Use OAuth2 authorization URL directly
                    loginUrl = `https://trade.pocketful.in/oauth2/auth?client_id={{broker_api_key}}&redirect_uri={{redirect_url}}&response_type=code&scope=${encodeURIComponent(scope)}&state=${encodeURIComponent(state)}`;
                    break;
                default:
                    alert('Please select a broker');
                    return;
            }

            window.location.href = loginUrl;
        });
    });
</script>
{% endblock %}

{% block content %}
<div class="min-h-[calc(100vh-8rem)] flex items-center justify-center py-8">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-16">
            <!-- Right side broker form - Shown first on mobile -->
            <div class="card flex-shrink-0 w-full max-w-md shadow-2xl bg-base-100 order-1 lg:order-2">
                <div class="card-body">
                    <div class="flex justify-center mb-6">
                        <img class="h-20 w-auto" src="{{ url_for('static', filename='favicon/apple-touch-icon.png') }}" alt="OpenAlgo">
                    </div>
                    <h2 class="card-title text-2xl font-bold text-center mb-6">Connect Your Trading Account</h2>
                    
                    <form id="trading-platform-form" class="space-y-6">
                        <div class="form-control w-full">
                            <label class="label">
                                <span class="label-text">Login with your Broker</span>
                            </label>
                            <select id="platform-select" name="platform" class="select select-bordered w-full">
                                <option value="" disabled selected>Select a Broker</option>
                                <option value="fivepaisa" {{ 'disabled' if broker_name != 'fivepaisa' }}>5 Paisa {{ '(Disabled)' if broker_name != 'fivepaisa' }}</option>
                                <option value="fivepaisaxts" {{ 'disabled' if broker_name != 'fivepaisaxts' }}>5 Paisa (XTS) {{ '(Disabled)' if broker_name != 'fivepaisaxts' }}</option>
                                <option value="aliceblue" {{ 'disabled' if broker_name != 'aliceblue' }}>Alice Blue {{ '(Disabled)' if broker_name != 'aliceblue' }}</option>
                                <option value="angel" {{ 'disabled' if broker_name != 'angel' }}>Angel One {{ '(Disabled)' if broker_name != 'angel' }}</option>
                                <option value="compositedge" {{ 'disabled' if broker_name != 'compositedge' }}>CompositEdge {{ '(Disabled)' if broker_name != 'compositedge' }}</option>
                                <option value="dhan" {{ 'disabled' if broker_name != 'dhan' }}>Dhan {{ '(Disabled)' if broker_name != 'dhan' }}</option>
                                <option value="dhan_sandbox" {{ 'disabled' if broker_name != 'dhan_sandbox' }}>Dhan (Sandbox) {{ '(Disabled)' if broker_name != 'dhan_sandbox' }}</option>
                                <option value="firstock" {{ 'disabled' if broker_name != 'firstock' }}>Firstock {{ '(Disabled)' if broker_name != 'firstock' }}</option>
                                <option value="flattrade" {{ 'disabled' if broker_name != 'flattrade' }}>Flattrade {{ '(Disabled)' if broker_name != 'flattrade' }}</option>
                                <option value="fyers" {{ 'disabled' if broker_name != 'fyers' }}>Fyers {{ '(Disabled)' if broker_name != 'fyers' }}</option>
                                <option value="groww" {{ 'disabled' if broker_name != 'groww' }}>Groww {{ '(Disabled)' if broker_name != 'groww' }}</option>
                                <option value="iifl" {{ 'disabled' if broker_name != 'iifl' }}>IIFL {{ '(Disabled)' if broker_name != 'iifl' }}</option>
                                <option value="jainam" {{ 'disabled' if broker_name != 'jainam' }}>Jainam (Retail) {{ '(Disabled)' if broker_name != 'jainam' }}</option>
                                <option value="jainampro" {{ 'disabled' if broker_name != 'jainampro' }}>Jainam (Dealer) {{ '(Disabled)' if broker_name != 'jainampro' }}</option>
                                <option value="kotak" {{ 'disabled' if broker_name != 'kotak' }}>Kotak Securities {{ '(Disabled)' if broker_name != 'kotak' }}</option>
                                <option value="paytm" {{ 'disabled' if broker_name != 'paytm' }}>Paytm Money {{ '(Disabled)' if broker_name != 'paytm' }}</option>
                                <option value="pocketful" {{ 'disabled' if broker_name != 'pocketful' }}>Pocketful {{ '(Disabled)' if broker_name != 'pocketful' }}</option>
                                <option value="shoonya" {{ 'disabled' if broker_name != 'shoonya' }}>Shoonya {{ '(Disabled)' if broker_name != 'shoonya' }}</option>
                                <option value="tradejini" {{ 'disabled' if broker_name != 'tradejini' }}>Tradejini {{ '(Disabled)' if broker_name != 'tradejini' }}</option>
                                <option value="upstox" {{ 'disabled' if broker_name != 'upstox' }}>Upstox {{ '(Disabled)' if broker_name != 'upstox' }}</option>
                                <option value="wisdom" {{ 'disabled' if broker_name != 'wisdom' }}>Wisdom Capital{{ '(Disabled)' if broker_name != 'wisdom' }}</option>
                                <option value="zebu" {{ 'disabled' if broker_name != 'zebu' }}>Zebu {{ '(Disabled)' if broker_name != 'zebu' }}</option>
                                <option value="zerodha" {{ 'disabled' if broker_name != 'zerodha' }}>Zerodha {{ '(Disabled)' if broker_name != 'zerodha' }}</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary w-full">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                            </svg>
                            Connect Account
                        </button>
                    </form>
                </div>
            </div>

            <!-- Left side content - Shown second on mobile -->
            <div class="flex-1 max-w-xl text-center lg:text-left order-2 lg:order-1">
                <h1 class="text-4xl lg:text-5xl font-bold mb-6">Connect Your <span class="text-primary">Broker</span></h1>
                <p class="text-lg lg:text-xl mb-8 opacity-80">
                    Link your trading account to start executing trades through OpenAlgo's algorithmic trading platform.
                </p>
                <div class="flex flex-col gap-4">
                    <div class="alert alert-info shadow-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="font-bold">Need Help?</h3>
                            <div class="text-sm">Check our documentation for broker setup guides.</div>
                        </div>
                    </div>
                    <div class="flex justify-center lg:justify-start gap-4">
                        <a href="https://docs.openalgo.in" target="_blank" rel="noopener noreferrer" class="btn btn-outline gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                            </svg>
                            Documentation
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
