{% extends "base.html" %}

{% block title %}Configure Symbols - {{ strategy.name }}{% endblock %}

{% block content %}
<!-- Delete Confirmation Modal -->
<dialog id="delete_confirm_modal" class="modal modal-bottom sm:modal-middle">
    <form method="dialog" class="modal-box">
        <h3 class="font-bold text-lg">Delete Symbol</h3>
        <p class="py-4">Are you sure you want to delete this symbol mapping?</p>
        <div class="modal-action">
            <button class="btn" id="cancel_delete">Cancel</button>
            <button class="btn btn-error" id="confirm_delete">Delete</button>
        </div>
    </form>
    <form method="dialog" class="modal-backdrop">
        <button>close</button>
    </form>
</dialog>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Configure Symbols - {{ strategy.name }}</h1>
        <div class="space-x-2">
            <a href="{{ url_for('chartink_bp.view_strategy', strategy_id=strategy.id) }}" class="btn">
                Back to Strategy
            </a>
        </div>
    </div>

    <div class="grid gap-6 lg:grid-cols-2">
        <!-- Single Symbol Form -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title">Add Single Symbol</h2>
                
                <form id="singleSymbolForm" class="space-y-4">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Symbol</span>
                            <span class="label-text-alt text-error">*</span>
                        </label>
                        <div class="relative">
                            <input type="text" id="symbolSearch" name="symbol" class="input input-bordered w-full" 
                                   placeholder="Search symbol..." required readonly>
                            <div id="searchResults" class="absolute z-10 w-full mt-1 hidden">
                                <ul class="menu bg-base-200 rounded-box shadow-lg max-h-60 overflow-y-auto">
                                    <!-- Search results will be populated here -->
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Exchange</span>
                            <span class="label-text-alt text-error">*</span>
                        </label>
                        <select name="exchange" class="select select-bordered" required>
                            <option value="NSE" selected>NSE</option>
                            <option value="BSE">BSE</option>
                        </select>
                    </div>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Quantity</span>
                            <span class="label-text-alt text-error">*</span>
                        </label>
                        <input type="number" name="quantity" class="input input-bordered" 
                               min="1" step="1" required>
                    </div>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Product Type</span>
                            <span class="label-text-alt text-error">*</span>
                        </label>
                        <select name="product_type" class="select select-bordered" required>
                            <option value="MIS">MIS (Intraday)</option>
                            <option value="CNC">CNC (Delivery)</option>
                        </select>
                    </div>

                    <div class="card-actions justify-end">
                        <button type="submit" class="btn btn-primary">Add Symbol</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Bulk Symbols Form -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title">Add Multiple Symbols</h2>
                
                <form id="bulkSymbolsForm" class="space-y-4">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Symbols (CSV format)</span>
                            <span class="label-text-alt text-error">*</span>
                        </label>
                        <textarea name="symbols" class="textarea textarea-bordered h-48" required
                                  placeholder="Format: Symbol,Exchange,Quantity,Product&#10;Example:&#10;SBIN,NSE,100,MIS&#10;INFY,NSE,50,CNC"></textarea>
                    </div>

                    <div class="alert alert-info">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                        <div>
                            <h3 class="font-bold">CSV Format</h3>
                            <p class="text-sm">Each line should contain: Symbol,Exchange,Quantity,Product</p>
                            <ul class="list-disc list-inside text-sm mt-2">
                                <li>Exchange: NSE or BSE</li>
                                <li>Product: MIS or CNC</li>
                                <li>Quantity: Whole numbers only</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card-actions justify-end">
                        <button type="submit" class="btn btn-primary">Add Symbols</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Current Mappings -->
    <div class="card bg-base-100 shadow-xl mt-6">
        <div class="card-body">
            <h2 class="card-title">Current Symbol Mappings</h2>

            {% if symbol_mappings %}
            <div class="overflow-x-auto">
                <table class="table table-zebra w-full" id="symbolTable">
                    <thead>
                        <tr>
                            <th>Symbol</th>
                            <th>Exchange</th>
                            <th>Quantity</th>
                            <th>Product</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for mapping in symbol_mappings %}
                        <tr data-mapping-id="{{ mapping.id }}">
                            <td>{{ mapping.chartink_symbol }}</td>
                            <td><div class="badge badge-ghost">{{ mapping.exchange }}</div></td>
                            <td>{{ mapping.quantity }}</td>
                            <td>{{ mapping.product_type }}</td>
                            <td>
                                <button class="btn btn-error btn-xs delete-symbol">
                                    Delete
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-8">
                <h3 class="text-lg font-semibold mb-2">No symbols configured</h3>
                <p class="text-base-content/70">Add symbols using the forms above</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const symbolSearch = document.getElementById('symbolSearch');
    const searchResults = document.getElementById('searchResults');
    const singleSymbolForm = document.getElementById('singleSymbolForm');
    const bulkSymbolsForm = document.getElementById('bulkSymbolsForm');
    const symbolTable = document.getElementById('symbolTable');
    const deleteModal = document.getElementById('delete_confirm_modal');
    let currentMappingId = null;
    let currentRow = null;

    // Symbol search with debouncing
    let debounceTimer;
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.className = 'input input-bordered w-full';
    searchInput.placeholder = 'Type to search...';
    
    symbolSearch.parentNode.insertBefore(searchInput, symbolSearch);
    symbolSearch.style.display = 'none';

    searchInput.addEventListener('input', function() {
        clearTimeout(debounceTimer);
        const query = this.value.trim();
        
        if (query.length < 2) {
            searchResults.classList.add('hidden');
            return;
        }

        debounceTimer = setTimeout(() => {
            const exchange = singleSymbolForm.querySelector('[name="exchange"]').value;
            
            fetch(`/chartink/search?q=${encodeURIComponent(query)}&exchange=${exchange}`)
                .then(response => response.json())
                .then(data => {
                    const results = data.results;
                    if (results.length > 0) {
                        const html = results.map(result => `
                            <li>
                                <a href="#" class="symbol-result flex items-center justify-between" 
                                   data-symbol="${result.symbol}"
                                   data-exchange="${result.exchange}">
                                    <div>
                                        <div class="font-medium">${result.symbol}</div>
                                        <div class="text-xs opacity-70">${result.name || 'N/A'}</div>
                                    </div>
                                    <div class="badge badge-ghost">${result.exchange}</div>
                                </a>
                            </li>
                        `).join('');
                        
                        searchResults.querySelector('ul').innerHTML = html;
                        searchResults.classList.remove('hidden');
                    } else {
                        searchResults.classList.add('hidden');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('error', 'Error searching symbols');
                });
        }, 300);
    });

    // Handle symbol selection
    document.addEventListener('click', function(e) {
        if (e.target.closest('.symbol-result')) {
            e.preventDefault();
            const result = e.target.closest('.symbol-result');
            symbolSearch.value = result.dataset.symbol;
            searchInput.value = result.dataset.symbol;
            singleSymbolForm.querySelector('[name="exchange"]').value = result.dataset.exchange;
            searchResults.classList.add('hidden');
        } else if (!e.target.closest('#searchResults')) {
            searchResults.classList.add('hidden');
        }
    });

    // Single symbol form submission
    singleSymbolForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!symbolSearch.value) {
            showToast('error', 'Please select a symbol from the search results');
            return;
        }

        const formData = {
            symbol: symbolSearch.value,
            exchange: this.querySelector('[name="exchange"]').value,
            quantity: this.querySelector('[name="quantity"]').value,
            product_type: this.querySelector('[name="product_type"]').value
        };

        if (!formData.exchange) {
            showToast('error', 'Please select an exchange');
            return;
        }

        if (!formData.quantity || formData.quantity <= 0) {
            showToast('error', 'Please enter a valid quantity');
            return;
        }

        fetch(`/chartink/{{ strategy.id }}/configure`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                location.reload();
            } else {
                showToast('error', data.error || 'Error adding symbol');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'Error adding symbol');
        });
    });

    // Bulk symbols form submission
    bulkSymbolsForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const symbols = this.querySelector('[name="symbols"]').value.trim();
        if (!symbols) {
            showToast('error', 'Please enter symbols in CSV format');
            return;
        }

        fetch(`/chartink/{{ strategy.id }}/configure`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({ symbols })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                location.reload();
            } else {
                showToast('error', data.error || 'Error adding symbols');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'Error adding symbols');
        });
    });

    // Handle symbol deletion
    if (symbolTable) {
        symbolTable.addEventListener('click', function(e) {
            if (e.target.classList.contains('delete-symbol')) {
                const row = e.target.closest('tr');
                const mappingId = row.dataset.mappingId;
                
                // Store the current mapping and row
                currentMappingId = mappingId;
                currentRow = row;
                
                // Show the modal
                deleteModal.showModal();
            }
        });
    }

    // Handle modal actions
    document.getElementById('cancel_delete').addEventListener('click', () => {
        currentMappingId = null;
        currentRow = null;
    });

    document.getElementById('confirm_delete').addEventListener('click', () => {
        if (currentMappingId && currentRow) {
            deleteSymbol(currentMappingId, currentRow);
        }
    });

    function deleteSymbol(mappingId, row) {
        fetch(`/chartink/{{ strategy.id }}/symbol/${mappingId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                row.remove();
                if (document.querySelector('#symbolTable tbody').children.length === 0) {
                    location.reload();
                }
                showToast('success', 'Symbol mapping deleted successfully');
            } else {
                showToast('error', data.error || 'Error deleting symbol mapping');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'Error deleting symbol mapping');
        });
    }
});

function showToast(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
    const icon = type === 'success' ? 
        '<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>' :
        '<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';

    const toast = document.createElement('div');
    toast.className = 'toast toast-end z-50';
    toast.innerHTML = `
        <div class="alert ${alertClass}">
            ${icon}
            <span>${message}</span>
        </div>
    `;
    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 3000);
}
</script>
{% endblock %}

{% block styles %}
<style>
.symbol-result {
    @apply block px-4 py-2 hover:bg-base-300 transition-colors duration-200;
}
</style>
{% endblock %}
{% endblock %}
