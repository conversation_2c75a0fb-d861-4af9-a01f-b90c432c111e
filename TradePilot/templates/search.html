{% extends "base.html" %}

{% block head %}
<style>
    .table-header {
        @apply sticky top-0 bg-base-200 z-10;
    }

    .sort-icon {
        @apply inline-block ml-1 opacity-50;
    }

    th.sortable {
        @apply cursor-pointer hover:bg-base-300 transition-colors duration-200;
    }

    .symbol-cell {
        @apply font-medium flex items-center gap-2;
    }

    .name-cell {
        @apply opacity-70;
    }

    .token-cell {
        @apply font-mono text-sm;
    }

    .numeric-cell {
        @apply font-mono text-right;
    }

    .copy-btn {
        @apply btn btn-ghost btn-xs p-1;
    }

    .copy-btn:hover {
        @apply text-primary;
    }
</style>
{% endblock %}

{% block content %}
<div class="w-full">
    <!-- Search Header -->
    <div class="card bg-base-100 shadow-lg mb-6">
        <div class="card-body">
            <div class="flex justify-between items-center mb-4">
                <h1 class="text-2xl font-bold">Search Results</h1>
                <a href="{{ url_for('search_bp.token') }}" class="btn btn-ghost btn-sm gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    New Search
                </a>
            </div>
            <div class="stats shadow">
                <div class="stat">
                    <div class="stat-title">Found</div>
                    <div class="stat-value text-primary">{{ results|length }}</div>
                    <div class="stat-desc">matching symbols</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Table -->
    <div class="card bg-base-100 shadow-lg overflow-x-auto">
        <div class="card-body p-0">
            <table class="table table-zebra w-full">
                <thead class="table-header">
                    <tr>
                        <th class="sortable" data-sort="symbol">
                            Symbol
                            <svg xmlns="http://www.w3.org/2000/svg" class="sort-icon h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                            </svg>
                        </th>
                        <th class="sortable" data-sort="brsymbol">Broker Symbol</th>
                        <th class="sortable" data-sort="name">Name</th>
                        <th class="sortable" data-sort="exchange">Exchange</th>
                        <th class="sortable" data-sort="brexchange">Broker Exchange</th>
                        <th class="sortable" data-sort="token">Token</th>
                        <th class="sortable" data-sort="lotsize">Lot Size</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in results %}
                    <tr>
                        <td>
                            <div class="symbol-cell">
                                {{ row.symbol }}
                                <button class="copy-btn tooltip" data-tip="Copy symbol" onclick="copyToClipboard('{{ row.symbol }}', event)">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                    </svg>
                                </button>
                            </div>
                        </td>
                        <td>{{ row.brsymbol }}</td>
                        <td class="name-cell">{{ row.name }}</td>
                        <td><div class="badge badge-ghost">{{ row.exchange }}</div></td>
                        <td><div class="badge badge-ghost">{{ row.brexchange }}</div></td>
                        <td class="token-cell">{{ row.token }}</td>
                        <td class="numeric-cell">{{ row.lotsize or '-' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if results|length > 10 %}
    <div id="pagination" class="flex justify-center mt-8">
        <div class="join">
            <!-- Pagination will be populated by JavaScript -->
        </div>
    </div>
    {% endif %}
</div>

<script>
function copyToClipboard(text, event) {
    event.stopPropagation(); // Prevent row click event if any
    navigator.clipboard.writeText(text).then(() => {
        showToast('Symbol copied to clipboard', 'success');
    }).catch(err => {
        showToast('Failed to copy symbol', 'error');
        console.error('Failed to copy:', err);
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // Sorting functionality
    document.querySelectorAll('th.sortable').forEach(header => {
        header.addEventListener('click', () => {
            const table = header.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const index = Array.from(header.parentElement.children).indexOf(header);
            const sortKey = header.dataset.sort;
            
            // Get current sort direction
            const currentDirection = header.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            
            // Update sort direction indicators
            document.querySelectorAll('th').forEach(th => {
                th.removeAttribute('data-sort-direction');
            });
            header.setAttribute('data-sort-direction', newDirection);
            
            // Sort rows
            rows.sort((a, b) => {
                const aValue = a.children[index].textContent.trim();
                const bValue = b.children[index].textContent.trim();
                
                // Handle numeric values
                if (['lotsize'].includes(sortKey)) {
                    const aNum = parseFloat(aValue) || 0;
                    const bNum = parseFloat(bValue) || 0;
                    return newDirection === 'asc' ? aNum - bNum : bNum - aNum;
                }
                
                // Handle text values
                return newDirection === 'asc' 
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            });
            
            // Clear and repopulate tbody
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));
        });
    });

    // Pagination functionality
    const rowsPerPage = 10;
    const rows = document.querySelectorAll('tbody tr');
    const totalPages = Math.ceil(rows.length / rowsPerPage);
    let currentPage = 1;

    function showPage(pageNumber) {
        rows.forEach((row, index) => {
            row.style.display = (index >= (pageNumber - 1) * rowsPerPage && index < pageNumber * rowsPerPage) 
                ? '' 
                : 'none';
        });
    }

    function setupPagination() {
        const pagination = document.getElementById('pagination').querySelector('.join');
        if (!pagination) return;
        
        pagination.innerHTML = '';
        
        // Previous button
        const prevButton = document.createElement('button');
        prevButton.className = 'join-item btn btn-sm';
        prevButton.innerHTML = '«';
        prevButton.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                updatePagination();
            }
        });
        pagination.appendChild(prevButton);

        // Page buttons
        for (let i = 1; i <= totalPages; i++) {
            const button = document.createElement('button');
            button.className = `join-item btn btn-sm ${i === currentPage ? 'btn-active' : ''}`;
            button.textContent = i;
            button.addEventListener('click', () => {
                currentPage = i;
                updatePagination();
            });
            pagination.appendChild(button);
        }

        // Next button
        const nextButton = document.createElement('button');
        nextButton.className = 'join-item btn btn-sm';
        nextButton.innerHTML = '»';
        nextButton.addEventListener('click', () => {
            if (currentPage < totalPages) {
                currentPage++;
                updatePagination();
            }
        });
        pagination.appendChild(nextButton);
    }

    function updatePagination() {
        showPage(currentPage);
        
        // Update button states
        document.querySelectorAll('.join-item').forEach(button => {
            button.classList.remove('btn-active');
            if (button.textContent == currentPage) {
                button.classList.add('btn-active');
            }
        });
    }

    // Initialize pagination if needed
    if (rows.length > rowsPerPage) {
        setupPagination();
        showPage(1);
    }
});
</script>
{% endblock %}
