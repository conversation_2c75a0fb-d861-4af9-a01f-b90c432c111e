{% extends "base.html" %}

{% block title %}No Brokers Connected{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="text-center py-12">
        <div class="max-w-md mx-auto">
            <!-- Icon -->
            <svg class="w-24 h-24 mx-auto text-base-content/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
            </svg>
            
            <!-- Title -->
            <h3 class="text-xl font-semibold mb-2">No Brokers Connected</h3>
            
            <!-- Message -->
            <p class="text-base-content/70 mb-6">
                {{ message or 'You need to connect at least one broker to view ' + data_type + ' data.' }}
            </p>
            
            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/auth/broker" class="btn btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                    </svg>
                    Connect Your First Broker
                </a>
                
                <a href="/multibroker/dashboard" class="btn btn-outline">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Multi-Broker Dashboard
                </a>
            </div>
            
            <!-- Help Text -->
            <div class="mt-8 text-left">
                <div class="alert alert-info">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <h3 class="font-bold">Getting Started with Multi-Broker Trading</h3>
                        <div class="text-sm mt-2">
                            <ol class="list-decimal list-inside space-y-1">
                                <li>Connect your first broker using the button above</li>
                                <li>Add additional brokers for diversification</li>
                                <li>View consolidated {{ data_type or 'trading data' }} across all brokers</li>
                                <li>Manage strategies and allocations per broker</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
