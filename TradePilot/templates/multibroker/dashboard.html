{% extends "base.html" %}

{% block title %}Multi-Broker Dashboard{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold">Multi-Broker Dashboard</h1>
            <p class="text-base-content/70 mt-2">Manage multiple brokers and their strategies</p>
        </div>
        <div class="stats shadow">
            <div class="stat">
                <div class="stat-title">Connected Brokers</div>
                <div class="stat-value text-primary">{{ total_brokers }}</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="flex gap-4 mb-6">
        <a href="{{ url_for('strategy_bp.new_strategy') }}" class="btn btn-primary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            New Strategy
        </a>
        <a href="{{ url_for('auth.broker_login') }}" class="btn btn-outline">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
            </svg>
            Connect New Broker
        </a>
    </div>

    {% if brokers %}
    <!-- Broker Cards Grid -->
    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {% for broker in brokers %}
        <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
            <div class="card-body">
                <!-- Broker Header -->
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <h2 class="card-title text-xl">{{ broker.broker|title }}</h2>
                        <div class="badge badge-success badge-sm">Connected</div>
                    </div>
                    <div class="dropdown dropdown-end">
                        <label tabindex="0" class="btn btn-ghost btn-sm">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                            </svg>
                        </label>
                        <ul tabindex="0" class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52">
                            <li><a onclick="updatePriority('{{ broker.broker }}')">Update Priority</a></li>
                            <li><a onclick="toggleBroker('{{ broker.broker }}', false)" class="text-error">Disable</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Broker Stats -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div class="stat bg-base-200 rounded-lg p-3">
                        <div class="stat-title text-xs">Strategies</div>
                        <div class="stat-value text-lg">{{ broker.strategy_count }}</div>
                    </div>
                    <div class="stat bg-base-200 rounded-lg p-3">
                        <div class="stat-title text-xs">Priority</div>
                        <div class="stat-value text-lg">{{ broker.priority }}</div>
                    </div>
                </div>

                <!-- Strategy List -->
                {% if broker.strategies %}
                <div class="mb-4">
                    <h4 class="font-semibold mb-2">Active Strategies:</h4>
                    <div class="space-y-1">
                        {% for strategy in broker.strategies[:3] %}
                        <div class="flex justify-between items-center text-sm">
                            <span class="truncate">{{ strategy.name }}</span>
                            <div class="badge {% if strategy.is_active %}badge-success{% else %}badge-error{% endif %} badge-xs">
                                {{ 'Active' if strategy.is_active else 'Inactive' }}
                            </div>
                        </div>
                        {% endfor %}
                        {% if broker.strategy_count > 3 %}
                        <div class="text-xs text-base-content/70">
                            +{{ broker.strategy_count - 3 }} more strategies
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% else %}
                <div class="text-center py-4 text-base-content/70">
                    <p>No strategies assigned</p>
                </div>
                {% endif %}

                <!-- Action Buttons -->
                <div class="card-actions justify-end">
                    <a href="{{ url_for('multibroker_bp.broker_page', broker_name=broker.broker) }}" 
                       class="btn btn-primary btn-sm">
                        View Details
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="text-center py-12">
        <div class="max-w-md mx-auto">
            <svg class="w-24 h-24 mx-auto text-base-content/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
            </svg>
            <h3 class="text-xl font-semibold mb-2">No Brokers Connected</h3>
            <p class="text-base-content/70 mb-4">Connect your first broker to start trading with multiple accounts</p>
            <a href="{{ url_for('auth.broker_login') }}" class="btn btn-primary">
                Connect Your First Broker
            </a>
        </div>
    </div>
    {% endif %}
</div>

<!-- Priority Update Modal -->
<div id="priorityModal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg">Update Broker Priority</h3>
        <div class="py-4">
            <label class="label">
                <span class="label-text">Priority (1-10, higher = more preferred)</span>
            </label>
            <input type="number" id="priorityInput" class="input input-bordered w-full" min="1" max="10" value="1">
        </div>
        <div class="modal-action">
            <button class="btn" onclick="closePriorityModal()">Cancel</button>
            <button class="btn btn-primary" onclick="savePriority()">Save</button>
        </div>
    </div>
</div>

<script>
let currentBroker = null;

function updatePriority(broker) {
    currentBroker = broker;
    document.getElementById('priorityModal').classList.add('modal-open');
}

function closePriorityModal() {
    document.getElementById('priorityModal').classList.remove('modal-open');
    currentBroker = null;
}

function savePriority() {
    const priority = document.getElementById('priorityInput').value;
    
    fetch('/multibroker/api/update_priority', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            broker: currentBroker,
            priority: parseInt(priority)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating priority');
    });
    
    closePriorityModal();
}

function toggleBroker(broker, isActive) {
    const action = isActive ? 'enable' : 'disable';
    
    if (confirm(`Are you sure you want to ${action} ${broker}?`)) {
        fetch('/multibroker/api/toggle_broker', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                broker: broker,
                is_active: isActive
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while toggling broker');
        });
    }
}
</script>
{% endblock %}
