{% extends "base.html" %}

{% block title %}Multi-Broker Order Book{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold">Multi-Broker Order Book</h1>
            <p class="text-base-content/70 mt-2">View orders across all connected brokers</p>
        </div>
        <div class="flex gap-2">
            <button onclick="refreshAllData()" class="btn btn-outline btn-sm">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh All
            </button>
            <div class="dropdown dropdown-end">
                <label tabindex="0" class="btn btn-primary btn-sm">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.586V4z"></path>
                    </svg>
                    Filter: {{ selected_broker|title if selected_broker != 'all' else 'All Brokers' }}
                </label>
                <ul tabindex="0" class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52">
                    <li><a href="?broker=all" class="{{ 'active' if selected_broker == 'all' }}">All Brokers</a></li>
                    {% for broker in available_brokers %}
                    <li><a href="?broker={{ broker }}" class="{{ 'active' if selected_broker == broker }}">{{ broker|title }}</a></li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>

    <!-- Broker Tabs -->
    <div class="tabs tabs-boxed mb-6">
        <a class="tab {{ 'tab-active' if selected_broker == 'all' }}" href="?broker=all">All Brokers</a>
        {% for broker in available_brokers %}
        <a class="tab {{ 'tab-active' if selected_broker == broker }}" href="?broker={{ broker }}">{{ broker|title }}</a>
        {% endfor %}
    </div>

    <!-- Broker Data Sections -->
    {% for broker_result in broker_data %}
    <div class="mb-8 broker-section" data-broker="{{ broker_result.broker }}">
        <!-- Broker Header -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex items-center gap-3">
                <h2 class="text-xl font-semibold">{{ broker_result.broker|title }} Orders</h2>
                {% if broker_result.success %}
                <div class="badge badge-success">Connected</div>
                {% else %}
                <div class="badge badge-error">Error</div>
                {% endif %}
            </div>
            <div class="flex gap-2">
                <button onclick="refreshBrokerData('{{ broker_result.broker }}', 'orderbook')" 
                        class="btn btn-ghost btn-sm">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
                <a href="/multibroker/broker/{{ broker_result.broker }}" class="btn btn-ghost btn-sm">
                    View Dashboard
                </a>
            </div>
        </div>

        {% if broker_result.success and broker_result.data %}
            {% set orders = broker_result.data.data.orders if broker_result.data.data else [] %}
            {% set stats = broker_result.data.data.statistics if broker_result.data.data else {} %}
            
            <!-- Statistics Cards -->
            {% if stats %}
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div class="stat bg-base-100 shadow rounded-lg p-4">
                    <div class="stat-title text-xs">Total Orders</div>
                    <div class="stat-value text-lg">{{ stats.get('total_orders', 0) }}</div>
                </div>
                <div class="stat bg-base-100 shadow rounded-lg p-4">
                    <div class="stat-title text-xs">Executed</div>
                    <div class="stat-value text-lg text-success">{{ stats.get('executed_orders', 0) }}</div>
                </div>
                <div class="stat bg-base-100 shadow rounded-lg p-4">
                    <div class="stat-title text-xs">Pending</div>
                    <div class="stat-value text-lg text-warning">{{ stats.get('pending_orders', 0) }}</div>
                </div>
                <div class="stat bg-base-100 shadow rounded-lg p-4">
                    <div class="stat-title text-xs">Cancelled</div>
                    <div class="stat-value text-lg text-error">{{ stats.get('cancelled_orders', 0) }}</div>
                </div>
            </div>
            {% endif %}

            <!-- Orders Table -->
            {% if orders %}
            <div class="overflow-x-auto bg-base-100 rounded-lg shadow">
                <table class="table table-zebra w-full">
                    <thead>
                        <tr>
                            <th>Symbol</th>
                            <th>Exchange</th>
                            <th>Type</th>
                            <th>Quantity</th>
                            <th>Price</th>
                            <th>Status</th>
                            <th>Time</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders[:10] %}
                        <tr>
                            <td class="font-semibold">{{ order.get('trading_symbol', 'N/A') }}</td>
                            <td>{{ order.get('exchange', 'N/A') }}</td>
                            <td>
                                <div class="badge {{ 'badge-success' if order.get('transaction_type') == 'BUY' else 'badge-error' }}">
                                    {{ order.get('transaction_type', 'N/A') }}
                                </div>
                            </td>
                            <td>{{ order.get('quantity', 'N/A') }}</td>
                            <td>₹{{ order.get('price', 'N/A') }}</td>
                            <td>
                                <div class="badge 
                                    {% if order.get('status') == 'COMPLETE' %}badge-success
                                    {% elif order.get('status') == 'PENDING' %}badge-warning
                                    {% elif order.get('status') == 'CANCELLED' %}badge-error
                                    {% else %}badge-neutral{% endif %}">
                                    {{ order.get('status', 'N/A') }}
                                </div>
                            </td>
                            <td class="text-sm">{{ order.get('order_timestamp', 'N/A') }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                
                {% if orders|length > 10 %}
                <div class="p-4 text-center">
                    <span class="text-sm text-base-content/70">Showing 10 of {{ orders|length }} orders</span>
                    <a href="/multibroker/broker/{{ broker_result.broker }}" class="btn btn-link btn-sm">View All</a>
                </div>
                {% endif %}
            </div>
            {% else %}
            <div class="alert alert-info">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>No orders found for {{ broker_result.broker|title }}</span>
            </div>
            {% endif %}
        {% else %}
            <!-- Error State -->
            <div class="alert alert-error">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                    <h3 class="font-bold">Error loading data from {{ broker_result.broker|title }}</h3>
                    <div class="text-xs">{{ broker_result.error or 'Unknown error occurred' }}</div>
                </div>
            </div>
        {% endif %}
    </div>
    {% endfor %}

    {% if not broker_data %}
    <div class="text-center py-12">
        <div class="max-w-md mx-auto">
            <svg class="w-24 h-24 mx-auto text-base-content/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="text-xl font-semibold mb-2">No Order Data Available</h3>
            <p class="text-base-content/70 mb-4">Connect brokers to view order information</p>
            <a href="/auth/broker" class="btn btn-primary">Connect Broker</a>
        </div>
    </div>
    {% endif %}
</div>

<script>
function refreshAllData() {
    location.reload();
}

function refreshBrokerData(broker, dataType) {
    const button = event.target.closest('button');
    const originalContent = button.innerHTML;
    
    // Show loading state
    button.innerHTML = '<span class="loading loading-spinner loading-sm"></span>';
    button.disabled = true;
    
    fetch(`/multibroker/data/api/refresh/${dataType}?broker=${broker}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload the page to show updated data
                location.reload();
            } else {
                alert('Error refreshing data: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error refreshing data');
        })
        .finally(() => {
            button.innerHTML = originalContent;
            button.disabled = false;
        });
}

// Auto-refresh every 30 seconds
setInterval(() => {
    if (document.visibilityState === 'visible') {
        refreshAllData();
    }
}, 30000);
</script>
{% endblock %}
