{% extends "base.html" %}

{% block title %}{{ broker_name|title }} Dashboard{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <div class="breadcrumbs text-sm">
                <ul>
                    <li><a href="{{ url_for('multibroker_bp.dashboard') }}">Multi-Broker</a></li>
                    <li>{{ broker_name|title }}</li>
                </ul>
            </div>
            <h1 class="text-3xl font-bold">{{ broker_name|title }} Dashboard</h1>
        </div>
        <div class="flex gap-2">
            <div class="badge badge-success">Connected</div>
            <a href="{{ url_for('multibroker_bp.dashboard') }}" class="btn btn-outline btn-sm">
                Back to Overview
            </a>
        </div>
    </div>

    <!-- Broker Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="stat bg-base-100 shadow rounded-lg">
            <div class="stat-figure text-primary">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <div class="stat-title">Active Strategies</div>
            <div class="stat-value text-primary">{{ strategy_count }}</div>
        </div>

        <div class="stat bg-base-100 shadow rounded-lg">
            <div class="stat-figure text-secondary">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <div class="stat-title">API Calls Today</div>
            <div class="stat-value text-secondary">{{ broker_status.api_calls_today }}</div>
        </div>

        <div class="stat bg-base-100 shadow rounded-lg">
            <div class="stat-figure text-accent">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
            </div>
            <div class="stat-title">Orders Today</div>
            <div class="stat-value text-accent">{{ broker_status.orders_today }}</div>
        </div>

        <div class="stat bg-base-100 shadow rounded-lg">
            <div class="stat-figure text-success">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="stat-title">Last Ping</div>
            <div class="stat-value text-success text-sm">{{ broker_status.last_ping }}</div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex gap-4 mb-6">
        <button class="btn btn-primary" onclick="refreshData()">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Refresh Data
        </button>
        <a href="{{ url_for('strategy_bp.new_strategy') }}" class="btn btn-outline">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            New Strategy
        </a>
        <button class="btn btn-outline" onclick="viewPortfolio()">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            View Portfolio
        </button>
    </div>

    {% if strategies %}
    <!-- Strategies Table -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h2 class="card-title mb-4">Strategies for {{ broker_name|title }}</h2>
            
            <div class="overflow-x-auto">
                <table class="table table-zebra w-full">
                    <thead>
                        <tr>
                            <th>Strategy Name</th>
                            <th>Type</th>
                            <th>Mode</th>
                            <th>Status</th>
                            <th>Platform</th>
                            <th>Trading Hours</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for strategy in strategies %}
                        <tr>
                            <td>
                                <div class="font-bold">{{ strategy.name }}</div>
                                <div class="text-sm opacity-50">ID: {{ strategy.id }}</div>
                            </td>
                            <td>
                                <div class="badge {% if strategy.is_intraday %}badge-primary{% else %}badge-secondary{% endif %}">
                                    {{ 'Intraday' if strategy.is_intraday else 'Positional' }}
                                </div>
                            </td>
                            <td>
                                <div class="badge {% if strategy.trading_mode == 'LONG' %}badge-success{% elif strategy.trading_mode == 'SHORT' %}badge-warning{% else %}badge-info{% endif %}">
                                    {{ strategy.trading_mode }}
                                </div>
                            </td>
                            <td>
                                <div class="badge {% if strategy.is_active %}badge-success{% else %}badge-error{% endif %}">
                                    {{ 'Active' if strategy.is_active else 'Inactive' }}
                                </div>
                            </td>
                            <td>{{ strategy.platform|title }}</td>
                            <td>
                                {% if strategy.is_intraday %}
                                <div class="text-sm">
                                    <div>{{ strategy.start_time }} - {{ strategy.end_time }}</div>
                                    <div class="text-xs opacity-50">Square-off: {{ strategy.squareoff_time }}</div>
                                </div>
                                {% else %}
                                <span class="text-sm opacity-50">N/A</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="flex gap-2">
                                    <a href="{{ url_for('strategy_bp.view_strategy', strategy_id=strategy.id) }}" 
                                       class="btn btn-ghost btn-xs">View</a>
                                    <button onclick="toggleStrategy({{ strategy.id }})" 
                                            class="btn btn-ghost btn-xs {% if strategy.is_active %}text-error{% else %}text-success{% endif %}">
                                        {{ 'Disable' if strategy.is_active else 'Enable' }}
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body text-center py-12">
            <svg class="w-24 h-24 mx-auto text-base-content/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <h3 class="text-xl font-semibold mb-2">No Strategies Assigned</h3>
            <p class="text-base-content/70 mb-4">This broker doesn't have any strategies assigned yet</p>
            <a href="{{ url_for('strategy_bp.new_strategy') }}" class="btn btn-primary">
                Create Your First Strategy
            </a>
        </div>
    </div>
    {% endif %}
</div>

<script>
function refreshData() {
    // Show loading state
    const btn = event.target.closest('button');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Refreshing...';
    btn.disabled = true;
    
    // Simulate refresh (replace with actual API call)
    setTimeout(() => {
        location.reload();
    }, 1000);
}

function viewPortfolio() {
    // Redirect to portfolio page for this broker
    window.location.href = `/portfolio?broker={{ broker_name }}`;
}

function toggleStrategy(strategyId) {
    if (confirm('Are you sure you want to toggle this strategy?')) {
        fetch(`/strategy/toggle/${strategyId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to toggle strategy'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while toggling strategy');
        });
    }
}

// Auto-refresh every 30 seconds
setInterval(() => {
    // Update last ping time and other real-time data
    // This would typically make an AJAX call to get updated status
}, 30000);
</script>
{% endblock %}
