# api/funds.py

import os
import json
import httpx
from broker.dhan.api.order_api import get_positions
from broker.dhan.mapping.order_data import map_position_data
from utils.httpx_client import get_httpx_client
from broker.dhan.api.baseurl import get_url

def get_margin_data(auth_token):
    print(auth_token)
    """Fetch margin data from Dhan API using the provided auth token."""
    api_key = os.getenv('BROKER_API_KEY')
    
    # Get the shared httpx client with connection pooling
    client = get_httpx_client()
    
    headers = {
        'access-token': auth_token,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    }
    
    url = get_url("/v2/fundlimit")
    res = client.get(url, headers=headers)
    # Add status attribute for compatibility with existing codebase
    res.status = res.status_code
    margin_data = json.loads(res.text)

    print(f"Funds Details: {margin_data}")


    if margin_data.get('status') == 'error':
        # Log the error or return an empty dictionary to indicate failure
        print(f"Error fetching margin data: {margin_data.get('errors')}")
        return {}

    try:

        position_book = get_positions(auth_token)

        print(f'Positionbook : {position_book}')

        # Check if position_book is an error response
        if isinstance(position_book, dict) and position_book.get('errorType'):
            print(f"Error getting positions: {position_book.get('errorMessage', 'Unknown error')}")
            total_realised = 0
            total_unrealised = 0
        else:
            # If successful, process the positions
            #position_book = map_position_data(position_book)

            def sum_realised_unrealised(position_book):
                total_realised = 0
                total_unrealised = 0
                if isinstance(position_book, list):
                    total_realised = sum(position.get('realizedProfit', 0) for position in position_book)
                    total_unrealised = sum(position.get('unrealizedProfit', 0) for position in position_book)
                return total_realised, total_unrealised

            total_realised, total_unrealised = sum_realised_unrealised(position_book)
        
        # Construct and return the processed margin data with safe formatting
        available_balance = margin_data.get('availabelBalance') if margin_data else None
        collateral_amount = margin_data.get('collateralAmount') if margin_data else None
        utilized_amount = margin_data.get('utilizedAmount') if margin_data else None

        processed_margin_data = {
            "availablecash": "{:.2f}".format(available_balance) if available_balance is not None else "0.00",
            "collateral": "{:.2f}".format(collateral_amount) if collateral_amount is not None else "0.00",
            "m2munrealized": "{:.2f}".format(total_unrealised) if total_unrealised is not None else "0.00",
            "m2mrealized": "{:.2f}".format(total_realised) if total_realised is not None else "0.00",
            "utiliseddebits": "{:.2f}".format(utilized_amount) if utilized_amount is not None else "0.00",
        }
        return processed_margin_data
    except KeyError:
        # Return an empty dictionary in case of unexpected data structure
        return {}
