from sqlalchemy import create_engine, Column, Integer, String, Boolean, Foreign<PERSON>ey, DateTime, Float, UniqueConstraint
from sqlalchemy.orm import scoped_session, sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
import os
import logging

logger = logging.getLogger(__name__)

DATABASE_URL = os.getenv('DATABASE_URL')

engine = create_engine(
    DATABASE_URL,
    pool_size=50,
    max_overflow=100,
    pool_timeout=10
)

db_session = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=engine))
Base = declarative_base()
Base.query = db_session.query_property()

class Strategy(Base):
    """Model for trading strategies"""
    __tablename__ = 'strategies'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    webhook_id = Column(String(36), unique=True, nullable=False)  # UUID
    user_id = Column(String(255), nullable=False)
    platform = Column(String(50), nullable=False, default='tradingview')  # Platform type (tradingview, chartink, etc)
    is_active = Column(Boolean, default=True)
    is_intraday = Column(Boolean, default=True)
    trading_mode = Column(String(10), nullable=False, default='LONG')  # LONG, SHORT, or BOTH
    start_time = Column(String(5))  # HH:MM format
    end_time = Column(String(5))  # HH:MM format
    squareoff_time = Column(String(5))  # HH:MM format
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    symbol_mappings = relationship("StrategySymbolMapping", back_populates="strategy", cascade="all, delete-orphan")
    broker_mappings = relationship("StrategyBrokerMapping", back_populates="strategy", cascade="all, delete-orphan")

class StrategySymbolMapping(Base):
    """Model for symbol mappings in strategies"""
    __tablename__ = 'strategy_symbol_mappings'

    id = Column(Integer, primary_key=True)
    strategy_id = Column(Integer, ForeignKey('strategies.id'), nullable=False)
    symbol = Column(String(50), nullable=False)
    exchange = Column(String(10), nullable=False)
    quantity = Column(Integer, nullable=False)
    product_type = Column(String(10), nullable=False)  # MIS/CNC
    broker = Column(String(20), nullable=True)  # New: Specific broker for this symbol
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    strategy = relationship("Strategy", back_populates="symbol_mappings")

class StrategyBrokerMapping(Base):
    """Model for broker assignments to strategies"""
    __tablename__ = 'strategy_broker_mappings'

    id = Column(Integer, primary_key=True)
    strategy_id = Column(Integer, ForeignKey('strategies.id'), nullable=False)
    broker = Column(String(20), nullable=False)
    is_active = Column(Boolean, default=True)
    priority = Column(Integer, default=1)  # Priority for this broker in strategy execution
    allocation_percent = Column(Float, default=100.0)  # Percentage of strategy allocation
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    strategy = relationship("Strategy", back_populates="broker_mappings")

    # Unique constraint to prevent duplicate broker assignments
    __table_args__ = (
        UniqueConstraint('strategy_id', 'broker', name='unique_strategy_broker'),
    )

def init_db():
    """Initialize the database"""
    print("Initializing Strategy DB")
    Base.metadata.create_all(bind=engine)

def create_strategy(name, webhook_id, user_id, is_intraday=True, trading_mode='LONG', start_time=None, end_time=None, squareoff_time=None, platform='tradingview'):
    """Create a new strategy"""
    try:
        strategy = Strategy(
            name=name,
            webhook_id=webhook_id,
            user_id=user_id,
            is_intraday=is_intraday,
            trading_mode=trading_mode,
            start_time=start_time,
            end_time=end_time,
            squareoff_time=squareoff_time,
            platform=platform
        )
        db_session.add(strategy)
        db_session.commit()
        return strategy
    except Exception as e:
        logger.error(f"Error creating strategy: {str(e)}")
        db_session.rollback()
        return None

def get_strategy(strategy_id):
    """Get strategy by ID"""
    try:
        return Strategy.query.get(strategy_id)
    except Exception as e:
        logger.error(f"Error getting strategy {strategy_id}: {str(e)}")
        return None

def get_strategy_by_webhook_id(webhook_id):
    """Get strategy by webhook ID"""
    try:
        return Strategy.query.filter_by(webhook_id=webhook_id).first()
    except Exception as e:
        logger.error(f"Error getting strategy by webhook ID {webhook_id}: {str(e)}")
        return None

def get_all_strategies():
    """Get all strategies"""
    try:
        return Strategy.query.all()
    except Exception as e:
        logger.error(f"Error getting all strategies: {str(e)}")
        return []

def get_user_strategies(user_id):
    """Get all strategies for a user"""
    try:
        logger.info(f"Fetching strategies for user: {user_id}")
        strategies = Strategy.query.filter_by(user_id=user_id).all()
        logger.info(f"Found {len(strategies)} strategies")
        return strategies
    except Exception as e:
        logger.error(f"Error getting user strategies for {user_id}: {str(e)}")
        return []

def delete_strategy(strategy_id):
    """Delete strategy and its symbol mappings"""
    try:
        strategy = get_strategy(strategy_id)
        if not strategy:
            return False
        
        db_session.delete(strategy)
        db_session.commit()
        return True
    except Exception as e:
        logger.error(f"Error deleting strategy {strategy_id}: {str(e)}")
        db_session.rollback()
        return False

def toggle_strategy(strategy_id):
    """Toggle strategy active status"""
    try:
        strategy = get_strategy(strategy_id)
        if not strategy:
            return False
        
        strategy.is_active = not strategy.is_active
        db_session.commit()
        return True
    except Exception as e:
        logger.error(f"Error toggling strategy {strategy_id}: {str(e)}")
        db_session.rollback()
        return False

def update_strategy_times(strategy_id, start_time=None, end_time=None, squareoff_time=None):
    """Update strategy trading times"""
    try:
        strategy = Strategy.query.get(strategy_id)
        if strategy:
            if start_time is not None:
                strategy.start_time = start_time
            if end_time is not None:
                strategy.end_time = end_time
            if squareoff_time is not None:
                strategy.squareoff_time = squareoff_time
            db_session.commit()
            return True
        return False
    except Exception as e:
        logger.error(f"Error updating strategy times {strategy_id}: {str(e)}")
        db_session.rollback()
        return False

def add_symbol_mapping(strategy_id, symbol, exchange, quantity, product_type):
    """Add symbol mapping to strategy"""
    try:
        mapping = StrategySymbolMapping(
            strategy_id=strategy_id,
            symbol=symbol,
            exchange=exchange,
            quantity=quantity,
            product_type=product_type
        )
        db_session.add(mapping)
        db_session.commit()
        return mapping
    except Exception as e:
        logger.error(f"Error adding symbol mapping: {str(e)}")
        db_session.rollback()
        return None

def bulk_add_symbol_mappings(strategy_id, mappings):
    """Add multiple symbol mappings at once"""
    try:
        for mapping_data in mappings:
            mapping = StrategySymbolMapping(
                strategy_id=strategy_id,
                **mapping_data
            )
            db_session.add(mapping)
        db_session.commit()
        return True
    except Exception as e:
        logger.error(f"Error bulk adding symbol mappings: {str(e)}")
        db_session.rollback()
        return False

def get_symbol_mappings(strategy_id):
    """Get all symbol mappings for a strategy"""
    try:
        return StrategySymbolMapping.query.filter_by(strategy_id=strategy_id).all()
    except Exception as e:
        logger.error(f"Error getting symbol mappings: {str(e)}")
        return []

def delete_symbol_mapping(mapping_id):
    """Delete a symbol mapping"""
    try:
        mapping = StrategySymbolMapping.query.get(mapping_id)
        if mapping:
            db_session.delete(mapping)
            db_session.commit()
            return True
        return False
    except Exception as e:
        logger.error(f"Error deleting symbol mapping {mapping_id}: {str(e)}")
        db_session.rollback()
        return False

# Multi-Broker Strategy Functions
def add_broker_mapping(strategy_id, broker, is_active=True, priority=1, allocation_percent=100.0):
    """Add broker mapping to strategy"""
    try:
        mapping = StrategyBrokerMapping(
            strategy_id=strategy_id,
            broker=broker,
            is_active=is_active,
            priority=priority,
            allocation_percent=allocation_percent
        )
        db_session.add(mapping)
        db_session.commit()
        return mapping
    except Exception as e:
        logger.error(f"Error adding broker mapping: {str(e)}")
        db_session.rollback()
        return None

def get_strategy_brokers(strategy_id):
    """Get all broker mappings for a strategy"""
    try:
        return StrategyBrokerMapping.query.filter_by(strategy_id=strategy_id).all()
    except Exception as e:
        logger.error(f"Error getting strategy brokers: {str(e)}")
        return []

def get_active_strategy_brokers(strategy_id):
    """Get all active broker mappings for a strategy"""
    try:
        return StrategyBrokerMapping.query.filter_by(
            strategy_id=strategy_id,
            is_active=True
        ).order_by(StrategyBrokerMapping.priority.desc()).all()
    except Exception as e:
        logger.error(f"Error getting active strategy brokers: {str(e)}")
        return []

def update_broker_mapping(mapping_id, is_active=None, priority=None, allocation_percent=None):
    """Update broker mapping"""
    try:
        mapping = StrategyBrokerMapping.query.get(mapping_id)
        if mapping:
            if is_active is not None:
                mapping.is_active = is_active
            if priority is not None:
                mapping.priority = priority
            if allocation_percent is not None:
                mapping.allocation_percent = allocation_percent
            db_session.commit()
            return True
        return False
    except Exception as e:
        logger.error(f"Error updating broker mapping {mapping_id}: {str(e)}")
        db_session.rollback()
        return False

def delete_broker_mapping(mapping_id):
    """Delete a broker mapping"""
    try:
        mapping = StrategyBrokerMapping.query.get(mapping_id)
        if mapping:
            db_session.delete(mapping)
            db_session.commit()
            return True
        return False
    except Exception as e:
        logger.error(f"Error deleting broker mapping {mapping_id}: {str(e)}")
        db_session.rollback()
        return False

def get_broker_strategies(user_id, broker_name):
    """Get all strategies assigned to a specific broker for a user"""
    try:
        # Join Strategy with StrategyBrokerMapping to get strategies for specific broker
        strategies = db_session.query(Strategy).join(
            StrategyBrokerMapping,
            Strategy.id == StrategyBrokerMapping.strategy_id
        ).filter(
            Strategy.user_id == user_id,
            StrategyBrokerMapping.broker == broker_name,
            StrategyBrokerMapping.is_active == True
        ).all()
        return strategies
    except Exception as e:
        logger.error(f"Error getting broker strategies: {str(e)}")
        return []
