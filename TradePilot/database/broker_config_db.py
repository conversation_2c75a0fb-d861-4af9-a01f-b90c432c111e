# database/broker_config_db.py

import os
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from cryptography.fernet import <PERSON>rne<PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import logging

logger = logging.getLogger(__name__)

DATABASE_URL = os.getenv('DATABASE_URL')
PEPPER = os.getenv('API_KEY_PEPPER', 'default-pepper-change-in-production')

def get_encryption_key():
    """Generate encryption key from pepper"""
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=b'broker_config_salt',
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(PEPPER.encode()))
    return Fernet(key)

# Initialize Fernet cipher
fernet = get_encryption_key()

engine = create_engine(DATABASE_URL, echo=False)
db_session = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=engine))
Base = declarative_base()
Base.query = db_session.query_property()

class BrokerConfig(Base):
    __tablename__ = 'broker_configs'
    id = Column(Integer, primary_key=True)
    user_id = Column(String, nullable=False)
    broker_name = Column(String, nullable=False)
    api_key_encrypted = Column(Text, nullable=False)
    api_secret_encrypted = Column(Text, nullable=False)
    additional_config_encrypted = Column(Text)  # For broker-specific configs like redirect_url, etc.
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())

def init_db():
    """Initialize broker config database"""
    print("Initializing Broker Config DB")
    Base.metadata.create_all(bind=engine)

def encrypt_data(data):
    """Encrypt sensitive data"""
    if not data:
        return ''
    return fernet.encrypt(data.encode()).decode()

def decrypt_data(encrypted_data):
    """Decrypt sensitive data"""
    if not encrypted_data:
        return ''
    try:
        return fernet.decrypt(encrypted_data.encode()).decode()
    except Exception as e:
        logger.error(f"Error decrypting data: {e}")
        return None

def store_broker_config(user_id, broker_name, api_key, api_secret, additional_config=None):
    """Store broker configuration for a user"""
    try:
        # Check if config already exists
        existing_config = BrokerConfig.query.filter_by(
            user_id=user_id, 
            broker_name=broker_name
        ).first()
        
        if existing_config:
            # Update existing config
            existing_config.api_key_encrypted = encrypt_data(api_key)
            existing_config.api_secret_encrypted = encrypt_data(api_secret)
            if additional_config:
                existing_config.additional_config_encrypted = encrypt_data(str(additional_config))
            existing_config.updated_at = func.now()
            existing_config.is_active = True
        else:
            # Create new config
            config = BrokerConfig(
                user_id=user_id,
                broker_name=broker_name,
                api_key_encrypted=encrypt_data(api_key),
                api_secret_encrypted=encrypt_data(api_secret),
                additional_config_encrypted=encrypt_data(str(additional_config)) if additional_config else None
            )
            db_session.add(config)
        
        db_session.commit()
        logger.info(f"Stored broker config for {broker_name} for user {user_id}")
        return True
    except Exception as e:
        logger.error(f"Error storing broker config: {e}")
        db_session.rollback()
        return False

def get_broker_config(user_id, broker_name):
    """Get broker configuration for a user"""
    try:
        config = BrokerConfig.query.filter_by(
            user_id=user_id,
            broker_name=broker_name,
            is_active=True
        ).first()
        
        if config:
            return {
                'api_key': decrypt_data(config.api_key_encrypted),
                'api_secret': decrypt_data(config.api_secret_encrypted),
                'additional_config': decrypt_data(config.additional_config_encrypted) if config.additional_config_encrypted else None
            }
        return None
    except Exception as e:
        logger.error(f"Error getting broker config: {e}")
        return None

def get_user_broker_configs(user_id):
    """Get all broker configurations for a user"""
    try:
        configs = BrokerConfig.query.filter_by(user_id=user_id, is_active=True).all()
        result = []
        for config in configs:
            result.append({
                'broker_name': config.broker_name,
                'api_key': decrypt_data(config.api_key_encrypted),
                'api_secret': decrypt_data(config.api_secret_encrypted),
                'additional_config': decrypt_data(config.additional_config_encrypted) if config.additional_config_encrypted else None,
                'created_at': config.created_at,
                'updated_at': config.updated_at
            })
        return result
    except Exception as e:
        logger.error(f"Error getting user broker configs: {e}")
        return []

def delete_broker_config(user_id, broker_name):
    """Delete broker configuration"""
    try:
        config = BrokerConfig.query.filter_by(
            user_id=user_id,
            broker_name=broker_name
        ).first()
        
        if config:
            config.is_active = False
            db_session.commit()
            logger.info(f"Deleted broker config for {broker_name} for user {user_id}")
            return True
        return False
    except Exception as e:
        logger.error(f"Error deleting broker config: {e}")
        db_session.rollback()
        return False

def ensure_broker_config_tables_exists():
    """Ensure broker config tables exist"""
    try:
        init_db()
        logger.info("Broker config tables initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing broker config tables: {e}")
