# database/multibroker_master_contract_db.py

"""
Multi-Broker Master Contract Database Management
===============================================

This module provides broker-specific master contract management to avoid conflicts
between different brokers' symbol data.

Features:
- Broker-specific symbol tables
- Isolated master contract downloads
- Broker-aware symbol search
- Automatic table management
"""

import logging
import os
from sqlalchemy import create_engine, Column, Integer, String, Float, Index, Sequence, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from flask import session
import pandas as pd

logger = logging.getLogger(__name__)

# Database setup
DATABASE_URL = os.getenv('DATABASE_URL')
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

class BrokerSymToken(Base):
    """
    Broker-specific symbol token table
    Each broker gets its own set of symbols with broker prefix
    """
    __tablename__ = 'broker_symtoken'
    
    id = Column(Integer, Sequence('broker_symtoken_id_seq'), primary_key=True)
    broker = Column(String, nullable=False, index=True)  # Broker name
    symbol = Column(String, nullable=False, index=True)
    brsymbol = Column(String, nullable=False, index=True)
    name = Column(String)
    exchange = Column(String, index=True)
    brexchange = Column(String, index=True)
    token = Column(String, index=True)
    expiry = Column(String)
    strike = Column(Float)
    lotsize = Column(Integer)
    instrumenttype = Column(String)
    tick_size = Column(Float)
    
    # Composite indexes for efficient querying
    __table_args__ = (
        Index('idx_broker_symbol_exchange', 'broker', 'symbol', 'exchange'),
        Index('idx_broker_symbol', 'broker', 'symbol'),
        Index('idx_broker_exchange', 'broker', 'exchange'),
    )

def init_multibroker_master_contract_db():
    """Initialize the multi-broker master contract database"""
    try:
        logger.info("Initializing Multi-Broker Master Contract DB")
        Base.metadata.create_all(bind=engine)
        logger.info("Multi-Broker Master Contract DB initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing multi-broker master contract DB: {e}")

def get_broker_symbol_count(broker_name):
    """Get the number of symbols for a specific broker"""
    try:
        db_session = SessionLocal()
        count = db_session.query(BrokerSymToken).filter(
            BrokerSymToken.broker == broker_name
        ).count()
        db_session.close()
        return count
    except Exception as e:
        logger.error(f"Error getting symbol count for {broker_name}: {e}")
        return 0

def delete_broker_symbols(broker_name):
    """Delete all symbols for a specific broker"""
    try:
        db_session = SessionLocal()
        deleted_count = db_session.query(BrokerSymToken).filter(
            BrokerSymToken.broker == broker_name
        ).delete()
        db_session.commit()
        db_session.close()
        logger.info(f"Deleted {deleted_count} symbols for broker {broker_name}")
        return True
    except Exception as e:
        logger.error(f"Error deleting symbols for {broker_name}: {e}")
        return False

def copy_broker_symbols_from_dataframe(broker_name, token_df):
    """Copy symbols from dataframe to broker-specific table"""
    try:
        db_session = SessionLocal()
        
        # Add broker column to dataframe
        token_df['broker'] = broker_name
        
        # Convert dataframe to list of dictionaries
        records = token_df.to_dict('records')
        
        # Insert records in batches for better performance
        batch_size = 1000
        total_records = len(records)
        
        for i in range(0, total_records, batch_size):
            batch = records[i:i + batch_size]
            
            # Create BrokerSymToken objects
            broker_symbols = []
            for record in batch:
                broker_symbol = BrokerSymToken(
                    broker=record.get('broker'),
                    symbol=record.get('symbol'),
                    brsymbol=record.get('brsymbol'),
                    name=record.get('name'),
                    exchange=record.get('exchange'),
                    brexchange=record.get('brexchange'),
                    token=record.get('token'),
                    expiry=record.get('expiry'),
                    strike=record.get('strike'),
                    lotsize=record.get('lotsize'),
                    instrumenttype=record.get('instrumenttype'),
                    tick_size=record.get('tick_size')
                )
                broker_symbols.append(broker_symbol)
            
            # Bulk insert
            db_session.bulk_save_objects(broker_symbols)
            db_session.commit()
            
            logger.info(f"Inserted batch {i//batch_size + 1}/{(total_records-1)//batch_size + 1} for {broker_name}")
        
        db_session.close()
        logger.info(f"Successfully copied {total_records} symbols for {broker_name}")
        return True
        
    except Exception as e:
        logger.error(f"Error copying symbols for {broker_name}: {e}")
        return False

def search_broker_symbols(broker_name, search_term, limit=50):
    """Search symbols for a specific broker"""
    try:
        db_session = SessionLocal()
        
        # Search in symbol, name, and brsymbol fields
        results = db_session.query(BrokerSymToken).filter(
            BrokerSymToken.broker == broker_name,
            (BrokerSymToken.symbol.ilike(f'%{search_term}%') |
             BrokerSymToken.name.ilike(f'%{search_term}%') |
             BrokerSymToken.brsymbol.ilike(f'%{search_term}%'))
        ).limit(limit).all()
        
        db_session.close()
        
        # Convert to list of dictionaries
        symbols = []
        for result in results:
            symbols.append({
                'symbol': result.symbol,
                'brsymbol': result.brsymbol,
                'name': result.name,
                'exchange': result.exchange,
                'brexchange': result.brexchange,
                'token': result.token,
                'expiry': result.expiry,
                'strike': result.strike,
                'lotsize': result.lotsize,
                'instrumenttype': result.instrumenttype,
                'tick_size': result.tick_size
            })
        
        return symbols
        
    except Exception as e:
        logger.error(f"Error searching symbols for {broker_name}: {e}")
        return []

def get_broker_symbol_info(broker_name, symbol, exchange):
    """Get specific symbol information for a broker"""
    try:
        db_session = SessionLocal()
        
        result = db_session.query(BrokerSymToken).filter(
            BrokerSymToken.broker == broker_name,
            BrokerSymToken.symbol == symbol,
            BrokerSymToken.exchange == exchange
        ).first()
        
        db_session.close()
        
        if result:
            return {
                'symbol': result.symbol,
                'brsymbol': result.brsymbol,
                'name': result.name,
                'exchange': result.exchange,
                'brexchange': result.brexchange,
                'token': result.token,
                'expiry': result.expiry,
                'strike': result.strike,
                'lotsize': result.lotsize,
                'instrumenttype': result.instrumenttype,
                'tick_size': result.tick_size
            }
        
        return None
        
    except Exception as e:
        logger.error(f"Error getting symbol info for {broker_name}: {e}")
        return None

def get_all_broker_symbols_status():
    """Get symbol count status for all brokers"""
    try:
        db_session = SessionLocal()
        
        # Get unique brokers and their symbol counts
        results = db_session.query(
            BrokerSymToken.broker,
            db_session.query(BrokerSymToken).filter(
                BrokerSymToken.broker == BrokerSymToken.broker
            ).count().label('symbol_count')
        ).group_by(BrokerSymToken.broker).all()
        
        db_session.close()
        
        broker_status = {}
        for result in results:
            broker_status[result.broker] = result.symbol_count
        
        return broker_status
        
    except Exception as e:
        logger.error(f"Error getting broker symbols status: {e}")
        return {}

def migrate_existing_symbols_to_broker(broker_name):
    """
    Migrate existing symbols from the old symtoken table to broker-specific table
    This is a one-time migration function
    """
    try:
        db_session = SessionLocal()
        
        # Check if old symtoken table exists and has data
        try:
            result = db_session.execute(text("SELECT COUNT(*) FROM symtoken"))
            old_count = result.scalar()
            
            if old_count > 0:
                logger.info(f"Found {old_count} symbols in old symtoken table, migrating to {broker_name}")
                
                # Copy data from old table to new broker-specific table
                db_session.execute(text(f"""
                    INSERT INTO broker_symtoken (broker, symbol, brsymbol, name, exchange, brexchange, token, expiry, strike, lotsize, instrumenttype, tick_size)
                    SELECT '{broker_name}', symbol, brsymbol, name, exchange, brexchange, token, expiry, strike, lotsize, instrumenttype, tick_size
                    FROM symtoken
                """))
                
                db_session.commit()
                logger.info(f"Successfully migrated {old_count} symbols to {broker_name}")
                
        except Exception as e:
            logger.info(f"Old symtoken table not found or empty: {e}")
        
        db_session.close()
        return True
        
    except Exception as e:
        logger.error(f"Error migrating symbols to {broker_name}: {e}")
        return False

# Convenience functions for current session broker
def get_current_broker():
    """Get the currently selected broker from session"""
    return session.get('broker', 'fyers')  # Default to fyers for backward compatibility

def search_current_broker_symbols(search_term, limit=50):
    """Search symbols for the currently selected broker"""
    broker_name = get_current_broker()
    return search_broker_symbols(broker_name, search_term, limit)

def get_current_broker_symbol_info(symbol, exchange):
    """Get symbol info for the currently selected broker"""
    broker_name = get_current_broker()
    return get_broker_symbol_info(broker_name, symbol, exchange)
