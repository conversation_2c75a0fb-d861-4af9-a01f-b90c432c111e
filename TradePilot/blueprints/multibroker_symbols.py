# blueprints/multibroker_symbols.py

"""
Multi-Broker Symbol Management Blueprint
=======================================

This blueprint provides API endpoints for managing symbols across multiple brokers.
Each broker maintains its own symbol database to avoid conflicts.
"""

import logging
from flask import Blueprint, request, jsonify, session
from utils.session import check_session_validity
from utils.multibroker_master_contract import (
    download_broker_master_contract,
    get_broker_master_contract_status,
    search_symbols_for_broker,
    search_symbols_for_current_broker,
    get_symbol_info_for_broker,
    get_all_brokers_master_contract_status,
    download_master_contract_for_configured_brokers
)
from database.multibroker_master_contract_db import get_current_broker

logger = logging.getLogger(__name__)

multibroker_symbols_bp = Blueprint('multibroker_symbols_bp', __name__, url_prefix='/multibroker/symbols')

@multibroker_symbols_bp.route('/search')
@check_session_validity
def search_symbols():
    """
    Search symbols for the currently selected broker or a specific broker
    
    Query Parameters:
        q (str): Search term
        broker (str, optional): Specific broker name
        limit (int, optional): Maximum results (default: 50)
    """
    try:
        search_term = request.args.get('q', '').strip()
        broker_name = request.args.get('broker')
        limit = int(request.args.get('limit', 50))
        
        if not search_term:
            return jsonify({
                'status': 'error',
                'message': 'Search term is required',
                'symbols': []
            })
        
        if len(search_term) < 2:
            return jsonify({
                'status': 'error',
                'message': 'Search term must be at least 2 characters',
                'symbols': []
            })
        
        # Use specific broker or current broker
        if broker_name:
            symbols = search_symbols_for_broker(broker_name, search_term, limit)
            used_broker = broker_name
        else:
            symbols = search_symbols_for_current_broker(search_term, limit)
            used_broker = get_current_broker()
        
        return jsonify({
            'status': 'success',
            'broker': used_broker,
            'search_term': search_term,
            'count': len(symbols),
            'symbols': symbols
        })
        
    except Exception as e:
        logger.error(f"Error searching symbols: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Search failed: {str(e)}',
            'symbols': []
        }), 500

@multibroker_symbols_bp.route('/info/<symbol>/<exchange>')
@check_session_validity
def get_symbol_info(symbol, exchange):
    """
    Get detailed information for a specific symbol
    
    Path Parameters:
        symbol (str): Symbol name
        exchange (str): Exchange name
        
    Query Parameters:
        broker (str, optional): Specific broker name
    """
    try:
        broker_name = request.args.get('broker')
        
        # Use specific broker or current broker
        if broker_name:
            symbol_info = get_symbol_info_for_broker(broker_name, symbol, exchange)
            used_broker = broker_name
        else:
            used_broker = get_current_broker()
            symbol_info = get_symbol_info_for_broker(used_broker, symbol, exchange)
        
        if symbol_info:
            return jsonify({
                'status': 'success',
                'broker': used_broker,
                'symbol_info': symbol_info
            })
        else:
            return jsonify({
                'status': 'error',
                'message': f'Symbol {symbol} not found in {exchange} for broker {used_broker}'
            }), 404
        
    except Exception as e:
        logger.error(f"Error getting symbol info: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Failed to get symbol info: {str(e)}'
        }), 500

@multibroker_symbols_bp.route('/download/<broker_name>', methods=['POST'])
@check_session_validity
def download_master_contract(broker_name):
    """
    Download master contract for a specific broker
    
    Path Parameters:
        broker_name (str): Name of the broker
    """
    try:
        user_id = session.get('user_id') or session.get('user')
        
        if not user_id:
            return jsonify({
                'status': 'error',
                'message': 'User not authenticated'
            }), 401
        
        logger.info(f"Starting master contract download for {broker_name} by user {user_id}")
        
        result = download_broker_master_contract(broker_name, user_id)
        
        if result['status'] == 'success':
            return jsonify(result)
        else:
            return jsonify(result), 400
        
    except Exception as e:
        logger.error(f"Error downloading master contract for {broker_name}: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Download failed: {str(e)}'
        }), 500

@multibroker_symbols_bp.route('/download/all', methods=['POST'])
@check_session_validity
def download_all_master_contracts():
    """
    Download master contracts for all configured brokers
    """
    try:
        user_id = session.get('user_id') or session.get('user')
        
        if not user_id:
            return jsonify({
                'status': 'error',
                'message': 'User not authenticated'
            }), 401
        
        logger.info(f"Starting master contract download for all configured brokers by user {user_id}")
        
        result = download_master_contract_for_configured_brokers(user_id)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error downloading all master contracts: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Download failed: {str(e)}'
        }), 500

@multibroker_symbols_bp.route('/status')
@check_session_validity
def get_master_contract_status():
    """
    Get master contract status for all brokers
    
    Query Parameters:
        broker (str, optional): Specific broker name
    """
    try:
        broker_name = request.args.get('broker')
        
        if broker_name:
            # Get status for specific broker
            status = get_broker_master_contract_status(broker_name)
            return jsonify({
                'status': 'success',
                'broker_status': {broker_name: status}
            })
        else:
            # Get status for all brokers
            all_status = get_all_brokers_master_contract_status()
            return jsonify({
                'status': 'success',
                'broker_status': all_status
            })
        
    except Exception as e:
        logger.error(f"Error getting master contract status: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Failed to get status: {str(e)}'
        }), 500

@multibroker_symbols_bp.route('/current-broker')
@check_session_validity
def get_current_broker_info():
    """
    Get information about the currently selected broker
    """
    try:
        current_broker = get_current_broker()
        status = get_broker_master_contract_status(current_broker)
        
        return jsonify({
            'status': 'success',
            'current_broker': current_broker,
            'broker_status': status
        })
        
    except Exception as e:
        logger.error(f"Error getting current broker info: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Failed to get current broker info: {str(e)}'
        }), 500

# Legacy compatibility endpoint
@multibroker_symbols_bp.route('/search_symbols')
@check_session_validity
def search_symbols_legacy():
    """
    Legacy symbol search endpoint for backward compatibility
    """
    try:
        search_term = request.args.get('symbol', '').strip()
        
        if not search_term:
            return jsonify([])
        
        symbols = search_symbols_for_current_broker(search_term, 50)
        
        # Convert to legacy format
        legacy_symbols = []
        for symbol in symbols:
            legacy_symbols.append({
                'symbol': symbol['symbol'],
                'name': symbol.get('name', ''),
                'exchange': symbol['exchange'],
                'token': symbol['token']
            })
        
        return jsonify(legacy_symbols)
        
    except Exception as e:
        logger.error(f"Error in legacy symbol search: {e}")
        return jsonify([])

# Error handlers
@multibroker_symbols_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        'status': 'error',
        'message': 'Endpoint not found'
    }), 404

@multibroker_symbols_bp.errorhandler(500)
def internal_error(error):
    return jsonify({
        'status': 'error',
        'message': 'Internal server error'
    }), 500
