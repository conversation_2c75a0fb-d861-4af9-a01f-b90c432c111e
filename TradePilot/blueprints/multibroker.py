from flask import Blueprint, render_template, request, jsonify, session, flash, redirect, url_for
from database.auth_db import (
    get_all_user_brokers, get_broker_auth, toggle_broker_status, 
    update_broker_priority, get_auth_token_broker
)
from database.strategy_db import (
    get_broker_strategies, add_broker_mapping, get_strategy_brokers,
    update_broker_mapping, delete_broker_mapping, get_active_strategy_brokers
)
from utils.session import check_session_validity
import logging

logger = logging.getLogger(__name__)

multibroker_bp = Blueprint('multibroker_bp', __name__, url_prefix='/multibroker')

def get_broker_oauth_url(broker_name):
    """Get OAuth URL for broker authentication"""
    import os

    # OAuth URLs for different brokers
    oauth_urls = {
        'fyers': lambda: f"https://api-t1.fyers.in/api/v3/generate-authcode?client_id={os.getenv('BROKER_API_KEY')}&redirect_uri={os.getenv('REDIRECT_URL')}&response_type=code&state=sample_state",
        'upstox': lambda: f"https://api.upstox.com/v2/login/authorization/dialog?response_type=code&client_id={os.getenv('BROKER_API_KEY')}&redirect_uri={os.getenv('REDIRECT_URL')}",
        'zerodha': lambda: f"https://kite.trade/connect/login?api_key={os.getenv('BROKER_API_KEY')}&v=3",
        'aliceblue': lambda: f"https://ant.aliceblueonline.com/oauth2/auth?response_type=code&state=test_state&client_id={os.getenv('BROKER_API_KEY')}&redirect_uri={os.getenv('REDIRECT_URL')}",
        'finvasia': lambda: f"https://trade.shoonya.com/NorenWClientTP/oauth2/auth?response_type=code&state=test_state&client_id={os.getenv('BROKER_API_KEY')}&redirect_uri={os.getenv('REDIRECT_URL')}",
        'mastertrust': lambda: f"https://masterswift-beta.mastertrust.co.in/oauth2/auth?response_type=code&state=test_state&client_id={os.getenv('BROKER_API_KEY')}&redirect_uri={os.getenv('REDIRECT_URL')}",
        'paytm': lambda: f"https://developer.paytmmoney.com/accounts/login/oauth/?response_type=code&state=test_state&client_id={os.getenv('BROKER_API_KEY')}&redirect_uri={os.getenv('REDIRECT_URL')}",
        'pocketful': lambda: f"https://openapi.pocketful.in/oauth2/auth?response_type=code&state=test_state&client_id={os.getenv('BROKER_API_KEY')}&redirect_uri={os.getenv('REDIRECT_URL')}",
        'compositedge': lambda: f"https://trade.compositeedge.com/oauth2/auth?response_type=code&state=test_state&client_id={os.getenv('BROKER_API_KEY')}&redirect_uri={os.getenv('REDIRECT_URL')}"
    }

    # Brokers that don't use OAuth (direct callback)
    direct_callback_brokers = ['dhan', 'dhan_sandbox', 'angel', 'fivepaisa', 'zebu', 'kotak', 'groww', 'wisdom', 'jainampro']

    if broker_name in direct_callback_brokers:
        return None  # Will use direct callback

    if broker_name in oauth_urls:
        try:
            return oauth_urls[broker_name]()
        except Exception as e:
            logger.error(f"Error generating OAuth URL for {broker_name}: {e}")
            return None

    logger.warning(f"No OAuth URL configured for broker: {broker_name}")
    return None

@multibroker_bp.route('/dashboard')
def dashboard():
    """Multi-broker dashboard showing all connected brokers and their strategies"""
    try:
        # Check if user is in session (partial login)
        if 'user' not in session:
            flash('Please log in to access the multi-broker dashboard', 'error')
            return redirect(url_for('auth.login'))

        # Get user_id from session or use username as fallback
        user_id = session.get('user_id') or session.get('user')
        if not user_id:
            flash('Please log in to access the multi-broker dashboard', 'error')
            return redirect(url_for('auth.login'))
        
        # Get all user's brokers
        brokers = get_all_user_brokers(user_id)

        # Get broker configurations
        from database.broker_config_db import get_user_broker_configs
        broker_configs = get_user_broker_configs(user_id)

        # Trigger automated master contract downloads on dashboard access
        try:
            from utils.automated_master_contract import trigger_automated_downloads_on_login
            download_result = trigger_automated_downloads_on_login(user_id)

            if download_result['status'] == 'success':
                if download_result.get('brokers_updating'):
                    flash(f"Downloading master contracts for {len(download_result['brokers_updating'])} brokers in background", 'info')
                    logger.info(f"Started automated downloads for user {user_id}: {download_result['brokers_updating']}")
            else:
                logger.warning(f"Master contract download trigger failed: {download_result.get('message')}")

        except Exception as e:
            logger.error(f"Error triggering automated master contract downloads: {e}")

        # Get strategies for each broker
        broker_data = []
        for broker in brokers:
            strategies = get_broker_strategies(user_id, broker['broker'])
            broker_data.append({
                'broker': broker['broker'],
                'auth_token': broker['auth_token'][:10] + '...' if broker['auth_token'] else None,
                'priority': broker['priority'],
                'last_used': broker['last_used'],
                'strategies': strategies,
                'strategy_count': len(strategies),
                'is_active': True,  # Since we only get active brokers
                'status': 'connected'
            })

        # If user has broker configs but no connected brokers, show them as configured
        if not broker_data and broker_configs:
            for config in broker_configs:
                broker_data.append({
                    'broker': config['broker_name'],
                    'auth_token': None,
                    'priority': 1,
                    'last_used': None,
                    'strategies': [],
                    'strategy_count': 0,
                    'is_active': False,
                    'status': 'configured'
                })

        return render_template('multibroker/dashboard.html',
                             brokers=broker_data,
                             total_brokers=len(broker_data),
                             broker_configs=broker_configs)
    
    except Exception as e:
        logger.error(f"Error in multi-broker dashboard: {str(e)}")
        flash('Error loading multi-broker dashboard', 'error')
        return redirect(url_for('core_bp.home'))

@multibroker_bp.route('/broker/<broker_name>')
def broker_page(broker_name):
    """Dynamic broker-specific page showing strategies and status"""
    try:
        # Check if user is in session (partial login)
        if 'user' not in session:
            flash('Please log in to access broker page', 'error')
            return redirect(url_for('auth.login'))

        # Get user_id from session or use username as fallback
        user_id = session.get('user_id') or session.get('user')
        if not user_id:
            flash('Please log in to access broker page', 'error')
            return redirect(url_for('auth.login'))
        
        # Get broker auth details
        broker_auth = get_broker_auth(user_id, broker_name)
        if not broker_auth:
            flash(f'Broker {broker_name} not found or not connected', 'error')
            return redirect(url_for('multibroker_bp.dashboard'))
        
        # Get strategies for this broker
        strategies = get_broker_strategies(user_id, broker_name)
        
        # Get broker connection status (simplified for now)
        broker_status = {
            'connected': True,
            'last_ping': 'Just now',
            'api_calls_today': 0,  # TODO: Implement API call tracking
            'orders_today': 0      # TODO: Implement order tracking
        }
        
        return render_template('multibroker/broker_page.html',
                             broker_name=broker_name,
                             strategies=strategies,
                             broker_status=broker_status,
                             strategy_count=len(strategies))
    
    except Exception as e:
        logger.error(f"Error in broker page for {broker_name}: {str(e)}")
        flash(f'Error loading {broker_name} page', 'error')
        return redirect(url_for('multibroker_bp.dashboard'))

@multibroker_bp.route('/select_broker/<broker_name>')
def select_broker(broker_name):
    """Select a broker and set it in session for single-broker compatibility"""
    try:
        # Check if user is in session
        if 'user' not in session:
            flash('Please log in to select a broker', 'error')
            return redirect(url_for('auth.login'))

        user_id = session.get('user_id') or session.get('user')
        if not user_id:
            flash('Please log in to select a broker', 'error')
            return redirect(url_for('auth.login'))

        # Check if user has this broker configured
        from database.broker_config_db import get_broker_config
        from database.auth_db import get_broker_auth

        broker_config = get_broker_config(user_id, broker_name)

        if not broker_config:
            flash(f'Broker {broker_name} not found in your configurations', 'error')
            return redirect(url_for('multibroker_bp.dashboard'))

        # Check if broker is authenticated (has valid auth token)
        broker_auth = get_broker_auth(user_id, broker_name)

        # Force re-authentication for testing (remove this later)
        force_reauth = request.args.get('force_auth', 'false').lower() == 'true'

        if not broker_auth or not broker_auth.get('auth_token') or force_reauth:
            # Broker needs authentication - redirect to OAuth flow
            logger.info(f"Broker {broker_name} needs authentication - redirecting to OAuth")
            flash(f'Authenticating {broker_name.title()}...', 'info')

            # Inject broker credentials for OAuth
            from utils.broker_credentials import inject_broker_credentials
            inject_broker_credentials(user_id, broker_name)

            # Redirect to broker-specific OAuth URL
            oauth_url = get_broker_oauth_url(broker_name)
            if oauth_url:
                return redirect(oauth_url)
            else:
                # For brokers that don't use OAuth (like Dhan), redirect to callback directly
                return redirect(url_for('brlogin_bp.broker_callback', broker=broker_name))

        # Set broker in session for single-broker compatibility
        session['broker'] = broker_name
        session['selected_broker'] = broker_name

        # Store auth token in session for immediate use
        session['AUTH_TOKEN'] = broker_auth['auth_token']
        if broker_auth.get('feed_token'):
            session['FEED_TOKEN'] = broker_auth['feed_token']

        flash(f'Selected {broker_name.title()} as active broker', 'success')

        # Redirect to the page they were trying to access or dashboard
        next_page = request.args.get('next')
        if next_page:
            return redirect(next_page)
        else:
            return redirect(url_for('dashboard_bp.dashboard'))

    except Exception as e:
        logger.error(f"Error selecting broker {broker_name}: {str(e)}")
        flash(f'Error selecting broker {broker_name}', 'error')
        return redirect(url_for('multibroker_bp.dashboard'))

@multibroker_bp.route('/api/toggle_broker', methods=['POST'])
@check_session_validity
def toggle_broker():
    """Toggle broker active status"""
    try:
        user_id = session.get('user_id')
        data = request.get_json()
        
        broker_name = data.get('broker')
        is_active = data.get('is_active', True)
        
        if not broker_name:
            return jsonify({'success': False, 'message': 'Broker name required'})
        
        success = toggle_broker_status(user_id, broker_name, is_active)
        
        if success:
            status = 'enabled' if is_active else 'disabled'
            return jsonify({
                'success': True, 
                'message': f'Broker {broker_name} {status} successfully'
            })
        else:
            return jsonify({
                'success': False, 
                'message': f'Failed to toggle broker {broker_name}'
            })
    
    except Exception as e:
        logger.error(f"Error toggling broker: {str(e)}")
        return jsonify({'success': False, 'message': 'Internal server error'})

@multibroker_bp.route('/api/update_priority', methods=['POST'])
@check_session_validity
def update_priority():
    """Update broker priority"""
    try:
        user_id = session.get('user_id')
        data = request.get_json()
        
        broker_name = data.get('broker')
        priority = data.get('priority', 1)
        
        if not broker_name:
            return jsonify({'success': False, 'message': 'Broker name required'})
        
        success = update_broker_priority(user_id, broker_name, priority)
        
        if success:
            return jsonify({
                'success': True, 
                'message': f'Broker {broker_name} priority updated to {priority}'
            })
        else:
            return jsonify({
                'success': False, 
                'message': f'Failed to update priority for {broker_name}'
            })
    
    except Exception as e:
        logger.error(f"Error updating broker priority: {str(e)}")
        return jsonify({'success': False, 'message': 'Internal server error'})

@multibroker_bp.route('/strategy/<int:strategy_id>/brokers')
@check_session_validity
def strategy_brokers(strategy_id):
    """Get brokers assigned to a strategy"""
    try:
        brokers = get_strategy_brokers(strategy_id)
        broker_data = []
        
        for broker in brokers:
            broker_data.append({
                'id': broker.id,
                'broker': broker.broker,
                'is_active': broker.is_active,
                'priority': broker.priority,
                'allocation_percent': broker.allocation_percent
            })
        
        return jsonify({'success': True, 'brokers': broker_data})
    
    except Exception as e:
        logger.error(f"Error getting strategy brokers: {str(e)}")
        return jsonify({'success': False, 'message': 'Internal server error'})

@multibroker_bp.route('/strategy/<int:strategy_id>/add_broker', methods=['POST'])
@check_session_validity
def add_strategy_broker(strategy_id):
    """Add broker to strategy"""
    try:
        data = request.get_json()
        
        broker_name = data.get('broker')
        priority = data.get('priority', 1)
        allocation_percent = data.get('allocation_percent', 100.0)
        
        if not broker_name:
            return jsonify({'success': False, 'message': 'Broker name required'})
        
        mapping = add_broker_mapping(strategy_id, broker_name, True, priority, allocation_percent)
        
        if mapping:
            return jsonify({
                'success': True, 
                'message': f'Broker {broker_name} added to strategy',
                'mapping_id': mapping.id
            })
        else:
            return jsonify({
                'success': False, 
                'message': f'Failed to add broker {broker_name} to strategy'
            })
    
    except Exception as e:
        logger.error(f"Error adding broker to strategy: {str(e)}")
        return jsonify({'success': False, 'message': 'Internal server error'})

@multibroker_bp.route('/api/broker_mapping/<int:mapping_id>', methods=['PUT', 'DELETE'])
@check_session_validity
def manage_broker_mapping(mapping_id):
    """Update or delete broker mapping"""
    try:
        if request.method == 'PUT':
            data = request.get_json()
            
            is_active = data.get('is_active')
            priority = data.get('priority')
            allocation_percent = data.get('allocation_percent')
            
            success = update_broker_mapping(mapping_id, is_active, priority, allocation_percent)
            
            if success:
                return jsonify({'success': True, 'message': 'Broker mapping updated'})
            else:
                return jsonify({'success': False, 'message': 'Failed to update broker mapping'})
        
        elif request.method == 'DELETE':
            success = delete_broker_mapping(mapping_id)
            
            if success:
                return jsonify({'success': True, 'message': 'Broker mapping deleted'})
            else:
                return jsonify({'success': False, 'message': 'Failed to delete broker mapping'})
    
    except Exception as e:
        logger.error(f"Error managing broker mapping: {str(e)}")
        return jsonify({'success': False, 'message': 'Internal server error'})
