from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from database.auth_db import get_all_user_brokers, get_broker_auth
from services.orderbook_service import get_orderbook_with_auth
from services.tradebook_service import get_tradebook_with_auth
from services.positionbook_service import get_positionbook_with_auth
from utils.session import check_session_validity
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback

logger = logging.getLogger(__name__)

multibroker_data_bp = Blueprint('multibroker_data_bp', __name__, url_prefix='/multibroker/data')

def fetch_broker_data(broker_info, data_type):
    """Fetch data from a specific broker"""
    try:
        broker_name = broker_info['broker']
        auth_token = broker_info['auth_token']
        
        if data_type == 'orderbook':
            success, data, status_code = get_orderbook_with_auth(auth_token, broker_name)
        elif data_type == 'tradebook':
            success, data, status_code = get_tradebook_with_auth(auth_token, broker_name)
        elif data_type == 'positions':
            success, data, status_code = get_positionbook_with_auth(auth_token, broker_name)
        else:
            return {
                'broker': broker_name,
                'success': False,
                'error': 'Invalid data type',
                'data': None
            }
        
        return {
            'broker': broker_name,
            'success': success,
            'data': data if success else None,
            'error': data.get('message') if not success else None,
            'status_code': status_code
        }
    except Exception as e:
        logger.error(f"Error fetching {data_type} for broker {broker_info['broker']}: {str(e)}")
        return {
            'broker': broker_info['broker'],
            'success': False,
            'error': str(e),
            'data': None
        }

@multibroker_data_bp.route('/orderbook')
@check_session_validity
def multi_orderbook():
    """Multi-broker orderbook view"""
    try:
        user_id = session.get('user')
        if not user_id:
            return redirect(url_for('auth.login'))
        
        # Get selected broker from query parameter
        selected_broker = request.args.get('broker', 'all')
        
        # Get all user's brokers
        user_brokers = get_all_user_brokers(user_id)
        
        if not user_brokers:
            return render_template('multibroker/no_brokers.html', 
                                 data_type='orderbook',
                                 message='No brokers connected. Please connect a broker first.')
        
        # Fetch orderbook data
        broker_data = []
        
        if selected_broker == 'all':
            # Fetch from all brokers concurrently
            with ThreadPoolExecutor(max_workers=5) as executor:
                future_to_broker = {
                    executor.submit(fetch_broker_data, broker, 'orderbook'): broker 
                    for broker in user_brokers
                }
                
                for future in as_completed(future_to_broker):
                    result = future.result()
                    broker_data.append(result)
        else:
            # Fetch from specific broker
            broker_info = next((b for b in user_brokers if b['broker'] == selected_broker), None)
            if broker_info:
                result = fetch_broker_data(broker_info, 'orderbook')
                broker_data.append(result)
        
        # Sort broker data by broker name
        broker_data.sort(key=lambda x: x['broker'])
        
        return render_template('multibroker/orderbook.html',
                             broker_data=broker_data,
                             selected_broker=selected_broker,
                             available_brokers=[b['broker'] for b in user_brokers])
    
    except Exception as e:
        logger.error(f"Error in multi-broker orderbook: {str(e)}")
        traceback.print_exc()
        return "Error loading multi-broker orderbook", 500

@multibroker_data_bp.route('/tradebook')
@check_session_validity
def multi_tradebook():
    """Multi-broker tradebook view"""
    try:
        user_id = session.get('user')
        if not user_id:
            return redirect(url_for('auth.login'))
        
        # Get selected broker from query parameter
        selected_broker = request.args.get('broker', 'all')
        
        # Get all user's brokers
        user_brokers = get_all_user_brokers(user_id)
        
        if not user_brokers:
            return render_template('multibroker/no_brokers.html', 
                                 data_type='tradebook',
                                 message='No brokers connected. Please connect a broker first.')
        
        # Fetch tradebook data
        broker_data = []
        
        if selected_broker == 'all':
            # Fetch from all brokers concurrently
            with ThreadPoolExecutor(max_workers=5) as executor:
                future_to_broker = {
                    executor.submit(fetch_broker_data, broker, 'tradebook'): broker 
                    for broker in user_brokers
                }
                
                for future in as_completed(future_to_broker):
                    result = future.result()
                    broker_data.append(result)
        else:
            # Fetch from specific broker
            broker_info = next((b for b in user_brokers if b['broker'] == selected_broker), None)
            if broker_info:
                result = fetch_broker_data(broker_info, 'tradebook')
                broker_data.append(result)
        
        # Sort broker data by broker name
        broker_data.sort(key=lambda x: x['broker'])
        
        return render_template('multibroker/tradebook.html',
                             broker_data=broker_data,
                             selected_broker=selected_broker,
                             available_brokers=[b['broker'] for b in user_brokers])
    
    except Exception as e:
        logger.error(f"Error in multi-broker tradebook: {str(e)}")
        traceback.print_exc()
        return "Error loading multi-broker tradebook", 500

@multibroker_data_bp.route('/positions')
@check_session_validity
def multi_positions():
    """Multi-broker positions view"""
    try:
        user_id = session.get('user')
        if not user_id:
            return redirect(url_for('auth.login'))
        
        # Get selected broker from query parameter
        selected_broker = request.args.get('broker', 'all')
        
        # Get all user's brokers
        user_brokers = get_all_user_brokers(user_id)
        
        if not user_brokers:
            return render_template('multibroker/no_brokers.html', 
                                 data_type='positions',
                                 message='No brokers connected. Please connect a broker first.')
        
        # Fetch positions data
        broker_data = []
        
        if selected_broker == 'all':
            # Fetch from all brokers concurrently
            with ThreadPoolExecutor(max_workers=5) as executor:
                future_to_broker = {
                    executor.submit(fetch_broker_data, broker, 'positions'): broker 
                    for broker in user_brokers
                }
                
                for future in as_completed(future_to_broker):
                    result = future.result()
                    broker_data.append(result)
        else:
            # Fetch from specific broker
            broker_info = next((b for b in user_brokers if b['broker'] == selected_broker), None)
            if broker_info:
                result = fetch_broker_data(broker_info, 'positions')
                broker_data.append(result)
        
        # Sort broker data by broker name
        broker_data.sort(key=lambda x: x['broker'])
        
        return render_template('multibroker/positions.html',
                             broker_data=broker_data,
                             selected_broker=selected_broker,
                             available_brokers=[b['broker'] for b in user_brokers])
    
    except Exception as e:
        logger.error(f"Error in multi-broker positions: {str(e)}")
        traceback.print_exc()
        return "Error loading multi-broker positions", 500

@multibroker_data_bp.route('/api/refresh/<data_type>')
@check_session_validity
def refresh_data(data_type):
    """API endpoint to refresh data for specific broker"""
    try:
        user_id = session.get('user')
        broker_name = request.args.get('broker')
        
        if not user_id or not broker_name:
            return jsonify({'success': False, 'message': 'Missing parameters'})
        
        # Get broker info
        user_brokers = get_all_user_brokers(user_id)
        broker_info = next((b for b in user_brokers if b['broker'] == broker_name), None)
        
        if not broker_info:
            return jsonify({'success': False, 'message': 'Broker not found'})
        
        # Fetch fresh data
        result = fetch_broker_data(broker_info, data_type)
        
        return jsonify(result)
    
    except Exception as e:
        logger.error(f"Error refreshing {data_type} for broker: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@multibroker_data_bp.route('/api/summary')
@check_session_validity
def get_summary():
    """Get summary data across all brokers"""
    try:
        user_id = session.get('user')
        if not user_id:
            return jsonify({'success': False, 'message': 'Not authenticated'})
        
        user_brokers = get_all_user_brokers(user_id)
        
        summary = {
            'total_brokers': len(user_brokers),
            'active_brokers': len([b for b in user_brokers if b.get('is_active', True)]),
            'brokers': []
        }
        
        # Get basic info for each broker
        for broker in user_brokers:
            broker_summary = {
                'name': broker['broker'],
                'priority': broker['priority'],
                'last_used': broker['last_used'].isoformat() if broker['last_used'] else None,
                'status': 'active' if broker.get('is_active', True) else 'inactive'
            }
            summary['brokers'].append(broker_summary)
        
        return jsonify({'success': True, 'data': summary})
    
    except Exception as e:
        logger.error(f"Error getting broker summary: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})
