from flask import Blueprint, render_template, session, redirect, url_for, g, jsonify, request
from database.auth_db import get_auth_token
from importlib import import_module
from utils.session import check_session_validity
import multiprocessing
import sys
import logging

logger = logging.getLogger(__name__)

def dynamic_import(broker):
    try:
        module_path = f'broker.{broker}.api.funds'
        module = import_module(module_path)
        get_margin_data = getattr(module, 'get_margin_data')
        return get_margin_data
    except ImportError as e:
        logger.error(f"Error importing module: {e}")
        return None

dashboard_bp = Blueprint('dashboard_bp', __name__, url_prefix='/')
scalper_process = None

@dashboard_bp.route('/dashboard')
@check_session_validity
def dashboard():
    login_username = session['user']
    user_id = session.get('user_id') or login_username

    # Check if broker is selected
    broker = session.get('broker') or session.get('selected_broker')
    if not broker:
        logger.error("Broker not set in session")
        # Redirect to broker selection instead of error
        return redirect(url_for('multibroker_bp.dashboard'))

    # Use multi-broker credential injection system
    try:
        from utils.broker_credentials import inject_broker_credentials

        # Inject credentials for the selected broker
        credentials_injected = inject_broker_credentials(user_id, broker)

        if not credentials_injected:
            logger.warning(f"Failed to inject credentials for broker {broker}")
            # Try to get auth token from old system as fallback
            AUTH_TOKEN = get_auth_token(login_username)
            if AUTH_TOKEN is None:
                logger.warning(f"No auth token found for user {login_username} and broker {broker}")
                return redirect(url_for('multibroker_bp.dashboard'))
        else:
            # Get auth token from session (injected by credential system)
            AUTH_TOKEN = session.get('AUTH_TOKEN')
            if not AUTH_TOKEN:
                logger.warning(f"No auth token in session after credential injection for {broker}")
                return redirect(url_for('multibroker_bp.dashboard'))

    except Exception as e:
        logger.error(f"Error with multi-broker credential injection: {e}")
        # Fallback to old system
        AUTH_TOKEN = get_auth_token(login_username)
        if AUTH_TOKEN is None:
            logger.warning(f"No auth token found for user {login_username}")
            return redirect(url_for('auth.logout'))

    get_margin_data_func = dynamic_import(broker)
    if get_margin_data_func is None:
        logger.error(f"Failed to import broker module for {broker}")
        return "Failed to import broker module", 500

    margin_data = get_margin_data_func(AUTH_TOKEN)
    return render_template('dashboard.html', margin_data=margin_data)
