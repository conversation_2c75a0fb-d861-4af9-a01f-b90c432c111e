# blueprints/broker_config.py

from flask import Blueprint, request, render_template, session, jsonify, flash, redirect, url_for
from database.broker_config_db import (
    store_broker_config, get_broker_config, get_user_broker_configs, 
    delete_broker_config
)
from utils.session import check_session_validity
import logging
import os

logger = logging.getLogger(__name__)

broker_config_bp = Blueprint('broker_config_bp', __name__, url_prefix='/broker-config')

# Broker-specific configuration requirements
BROKER_CONFIGS = {
    'zerodha': {
        'fields': ['api_key', 'api_secret'],
        'display_name': 'Zerodha',
        'description': 'Zerodha Kite API credentials'
    },
    'upstox': {
        'fields': ['api_key', 'api_secret', 'redirect_uri'],
        'display_name': 'Upstox',
        'description': 'Upstox API credentials'
    },
    'angel': {
        'fields': ['api_key', 'api_secret'],
        'display_name': 'Angel One',
        'description': 'Angel One SmartAPI credentials'
    },
    'fyers': {
        'fields': ['app_id', 'secret_key', 'redirect_uri'],
        'display_name': 'Fyers',
        'description': 'Fyers API credentials'
    },
    'aliceblue': {
        'fields': ['api_key', 'api_secret'],
        'display_name': 'Alice Blue',
        'description': 'Alice Blue API credentials'
    },
    'fivepaisa': {
        'fields': ['app_name', 'app_source', 'user_id', 'password', 'user_key', 'encryption_key'],
        'display_name': '5Paisa',
        'description': '5Paisa API credentials'
    },
    'dhan': {
        'fields': ['client_id', 'access_token'],
        'display_name': 'Dhan',
        'description': 'Dhan API credentials'
    },
    'shoonya': {
        'fields': ['api_key', 'api_secret'],
        'display_name': 'Shoonya',
        'description': 'Shoonya API credentials'
    },
    'flattrade': {
        'fields': ['api_key', 'api_secret'],
        'display_name': 'Flattrade',
        'description': 'Flattrade API credentials'
    },
    'kotak': {
        'fields': ['consumer_key', 'access_token', 'consumer_secret'],
        'display_name': 'Kotak Securities',
        'description': 'Kotak Securities API credentials'
    }
}

@broker_config_bp.route('/')
@check_session_validity
def list_configs():
    """List all broker configurations for the user"""
    try:
        user_id = session.get('user_id') or session.get('user')
        if not user_id:
            flash('Please log in to manage broker configurations', 'error')
            return redirect(url_for('auth.login'))
        
        configs = get_user_broker_configs(user_id)
        return render_template('broker_config/list.html', 
                             configs=configs, 
                             broker_configs=BROKER_CONFIGS)
    except Exception as e:
        logger.error(f"Error listing broker configs: {str(e)}")
        flash('Error loading broker configurations', 'error')
        return redirect(url_for('multibroker_bp.dashboard'))

@broker_config_bp.route('/add/<broker_name>')
@check_session_validity
def add_config_form(broker_name):
    """Show form to add broker configuration"""
    try:
        user_id = session.get('user_id') or session.get('user')
        if not user_id:
            flash('Please log in to add broker configuration', 'error')
            return redirect(url_for('auth.login'))
        
        if broker_name not in BROKER_CONFIGS:
            flash(f'Broker {broker_name} is not supported', 'error')
            return redirect(url_for('broker_config_bp.list_configs'))
        
        broker_info = BROKER_CONFIGS[broker_name]
        existing_config = get_broker_config(user_id, broker_name)
        
        return render_template('broker_config/add.html',
                             broker_name=broker_name,
                             broker_info=broker_info,
                             existing_config=existing_config)
    except Exception as e:
        logger.error(f"Error showing add config form: {str(e)}")
        flash('Error loading configuration form', 'error')
        return redirect(url_for('broker_config_bp.list_configs'))

@broker_config_bp.route('/save', methods=['POST'])
@check_session_validity
def save_config():
    """Save broker configuration"""
    try:
        user_id = session.get('user_id') or session.get('user')
        if not user_id:
            return jsonify({'success': False, 'message': 'Please log in'})
        
        data = request.get_json()
        broker_name = data.get('broker_name')
        
        if broker_name not in BROKER_CONFIGS:
            return jsonify({'success': False, 'message': 'Unsupported broker'})
        
        broker_info = BROKER_CONFIGS[broker_name]
        config_data = {}
        
        # Extract required fields
        for field in broker_info['fields']:
            value = data.get(field)
            if not value:
                return jsonify({'success': False, 'message': f'{field} is required'})
            config_data[field] = value
        
        # For backward compatibility, map to standard fields
        api_key = config_data.get('api_key') or config_data.get('app_id') or config_data.get('client_id') or config_data.get('consumer_key')
        api_secret = config_data.get('api_secret') or config_data.get('secret_key') or config_data.get('access_token') or config_data.get('consumer_secret')
        
        if not api_key or not api_secret:
            return jsonify({'success': False, 'message': 'API key and secret are required'})
        
        # Store configuration
        success = store_broker_config(user_id, broker_name, api_key, api_secret, config_data)
        
        if success:
            return jsonify({'success': True, 'message': f'{broker_name} configuration saved successfully'})
        else:
            return jsonify({'success': False, 'message': 'Failed to save configuration'})
            
    except Exception as e:
        logger.error(f"Error saving broker config: {str(e)}")
        return jsonify({'success': False, 'message': 'Internal server error'})

@broker_config_bp.route('/delete/<broker_name>', methods=['POST'])
@check_session_validity
def delete_config(broker_name):
    """Delete broker configuration"""
    try:
        user_id = session.get('user_id') or session.get('user')
        if not user_id:
            return jsonify({'success': False, 'message': 'Please log in'})
        
        success = delete_broker_config(user_id, broker_name)
        
        if success:
            return jsonify({'success': True, 'message': f'{broker_name} configuration deleted'})
        else:
            return jsonify({'success': False, 'message': 'Configuration not found'})
            
    except Exception as e:
        logger.error(f"Error deleting broker config: {str(e)}")
        return jsonify({'success': False, 'message': 'Internal server error'})

@broker_config_bp.route('/test/<broker_name>')
@check_session_validity
def test_config(broker_name):
    """Test broker configuration"""
    try:
        user_id = session.get('user_id') or session.get('user')
        if not user_id:
            return jsonify({'success': False, 'message': 'Please log in'})
        
        config = get_broker_config(user_id, broker_name)
        if not config:
            return jsonify({'success': False, 'message': 'Configuration not found'})
        
        # TODO: Implement actual broker connection testing
        # For now, just return success if config exists
        return jsonify({'success': True, 'message': f'{broker_name} configuration is valid'})
        
    except Exception as e:
        logger.error(f"Error testing broker config: {str(e)}")
        return jsonify({'success': False, 'message': 'Internal server error'})

def get_broker_credentials(user_id, broker_name):
    """Helper function to get broker credentials for authentication"""
    try:
        config = get_broker_config(user_id, broker_name)
        if config:
            return config['api_key'], config['api_secret'], config.get('additional_config')
        
        # Fallback to environment variables for backward compatibility
        if broker_name == os.getenv('REDIRECT_URL', '').split('/')[-2]:
            return os.getenv('BROKER_API_KEY'), os.getenv('BROKER_API_SECRET'), None
        
        return None, None, None
    except Exception as e:
        logger.error(f"Error getting broker credentials: {str(e)}")
        return None, None, None
