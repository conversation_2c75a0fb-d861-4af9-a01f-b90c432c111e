services:
  openalgo:
    image: openalgo:latest
    build:
      context: .
      dockerfile: Dockerfile

    container_name: openalgo-web
    ports:
      - "${FLASK_PORT:-5000}:5000"

    # persistent DB + mount the host .env read-only so dotenv can read it
    volumes:
      - ./db:/app/db
      - ./.env:/app/.env:ro   # ← .env stays on your machine

    # (optional) extra env-vars that are NOT in .env
    environment:
      - FLASK_ENV=${FLASK_ENV:-production}
      - FLASK_DEBUG=${FLASK_DEBUG:-0}

    restart: unless-stopped
