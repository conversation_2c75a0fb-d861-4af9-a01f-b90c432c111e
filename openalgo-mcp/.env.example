# OpenAlgo MCP - Environment Configuration
# =====================================

# ===== REQUIRED CONFIGURATION =====

# OpenAlgo API Configuration
# --------------------------
# Your OpenAlgo API key (required)
OPENALGO_API_KEY=your_openalgo_api_key_here
# OpenAlgo API host URL (default: http://127.0.0.1:5000)
OPENALGO_API_HOST=http://127.0.0.1:5000

# ===== SERVER CONFIGURATION =====

# Server settings
# ---------------
# Port for the MCP server (default: 8001)
SERVER_PORT=8001
# Server mode - 'stdio' or 'sse' (default: sse)
SERVER_MODE=sse
# Set to 'true' to enable debug mode
SERVER_DEBUG=false

# ===== CLIENT CONFIGURATION =====

# LLM Configuration
# -------------------
# Choose the LLM provider to use (default: openai) - options: openai, groq
LLM_PROVIDER=openai

# OpenAI Configuration
# -------------------
# OpenAI API key for the AGNO agent (required if using OpenAI)
OPENAI_API_KEY=your_openai_api_key_here
# OpenAI model to use (default: gpt-4o)
OPENAI_MODEL=gpt-4o

# GROQ Configuration
# -------------------
# GROQ API key (required if using GROQ)
GROQ_API_KEY=your_groq_api_key_here
# GROQ model to use (default: llama-3.1-70b-versatile)
GROQ_MODEL=llama-3.3-70b-versatile

# Client MCP Connection
# --------------------
# MCP server host (default: localhost)
MCP_HOST=localhost
# MCP server port (default: 8001) - should match SERVER_PORT
MCP_PORT=8001
