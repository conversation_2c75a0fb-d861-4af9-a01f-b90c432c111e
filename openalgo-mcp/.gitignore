# Python
__pycache__/
*.py[cod]
*$py.class
*.cpython-*.pyc
*.cpython-312.pyc
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environments
venv/
env/
ENV/
.venv
env.bak/
venv.bak/

# Jupyter Notebooks
.ipynb_checkpoints

# IDE files
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Local configuration
.env
*.env.local
*.env.development.local
*.env.test.local
*.env.production.local

# OpenAlgo specific
client/.env
server/.env

# Exceptions - include example files
!.env.example
